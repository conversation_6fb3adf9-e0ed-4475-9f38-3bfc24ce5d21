//
// Created by <PERSON><PERSON> on 2024/9/12.
//

#include "comp_setting_item.h"
#include "page_manager_anim_mode.h"
#include "comp_status_bar.h"
#include "page_setting_detail.h"
#include "view_model.h"
#include "comp_border_focused.h"
#include "utils.h"
#include "multi_language.h"
//#include "system.h"

#define CH_Str_Length_Max 3
#define MY_IMG_OPA  LV_OPA_40
#define MY_LABEL_OPA    LV_OPA_70

typedef enum {
    item_normal,
    item_list,
    item_grid
} ItemTypeEnum;

// 子项配置结构体
typedef struct {
    const ItemTypeEnum item_type;
    const void *icon;
    char *title;
    union {
        char *text;            // 静态文本，不需要翻译
        TextIdEnum text_id;    // 需要翻译的文本ID
    };
    uint8_t type;
    bool need_translate;       // 是否需要翻译
} SubItemConfig;

const uint8_t ch_arr[CH_MAX] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10,
                                11, 12, 13, 14, 15, 16, 17, 18, 19, 20,
                                21, 22, 23, 24, 25, 26, 27, 28, 29, 30,
                                31, 32};

/**
 * @brief 更新 grid_title 文字
 * @param e
 */
void setting_grid_title_cb(lv_event_t *e) {
    lv_obj_t *tar_obj = lv_event_get_target(e);
    if (tar_obj != SettingObjs.item[setting_language].grid_title_bg) {
        return;
    }
    lv_event_code_t code = lv_event_get_code(e);
    if (code == LV_EVENT_VALUE_CHANGED) {
        lv_obj_t *label_obj = lv_obj_get_child(tar_obj, 0);
        lv_label_set_text(label_obj, get_text(text_language));
    }
}

// 切换输入组，用于弹出页
void switch_group_handle(PageTypeHandle *page) {
    if (page->indev_group != NULL) {
        lv_group_remove_all_objs(page->indev_group);
        lv_group_del(page->indev_group);
        page->indev_group = NULL;
    }
    page->indev_group = lv_group_create();
    set_indev_group(page->indev_group);
}

// 清空组对象
void clean_group_obj_arr(PageTypeHandle *page) {
    // 清空组obj指针数组
    for (int i = 0; i < GROUP_OBJ_MAX; i++) {
        if (page->group_obj[i] != NULL) {
            // 将指针设置为NULL
            page->group_obj[i] = NULL;
        }
    }
}

// 自动zoom obj回调
static void auto_event_cb(lv_event_t *e) {
    lv_obj_t *tar_obj = lv_event_get_target(e);
    lv_event_code_t code = lv_event_get_code(e);
    // static bool is_short_clicked;
    // static uint16_t count_pressing;
    // if (code == LV_EVENT_PRESSING) {
    //     count_pressing++;
    //     if (count_pressing > Short_Click_Sensitivity) {
    //         is_short_clicked = false;
    //     } else {
    //         is_short_clicked = true;
    //     }
    // } else if (code == LV_EVENT_RELEASED) {
    //     count_pressing = 0;
    // } else
    if (code == LV_EVENT_SHORT_CLICKED || code == LV_EVENT_KEY) {
        LV_LOG("auto LV_EVENT_SHORT_CLICKED\n");
        if (code == LV_EVENT_KEY) {
            char c = *((char *) lv_event_get_param(e));
            if (c == LV_KEY_HOME || c == LV_KEY_END) {
                page_click_anim(pos_left, &Pages.page[Page_Setting], anim_overlay);
            }
            if (c != LV_KEY_ENTER) {
                return;
            }
        }
        // if (is_short_clicked || code == LV_EVENT_KEY) {
        // if (code == LV_EVENT_KEY) {
        lv_obj_set_style_bg_color(tar_obj, lv_color_hex(Main_Color), 0);
        Setting.zoom = zoom_auto;
        for (int i = 0; i < GroupItemCount; ++i) {
            Param.group[i].is_auto_zoom = true;
        }
        lv_event_send(tar_obj, LV_EVENT_VALUE_CHANGED, NULL);
        lv_event_send(SettingObjs.item[setting_zoom].sub_item[0].obj_bg, LV_EVENT_VALUE_CHANGED, NULL);
        // }
    } else if (code == LV_EVENT_VALUE_CHANGED) {
        set_zoom_level(GroupItemCount);
    }
}

/**
 * @brief 检查组内zoom_level和is_auto_zoom状态
 * @return 0xFF全为自动，Zoom_Item_Count 不选中，Else: 对应zoom值
 */
static uint8_t check_group_zoom_status() {
    // 检查is_auto_zoom是否都相等
    bool first_auto_zoom = Param.group[0].is_auto_zoom;
    bool all_same_auto_zoom = true;
    for (int i = 1; i < GroupItemCount; i++) {
        // 避免未添加进列表的组影响判断
        if (Param.group[i].is_added_to_list) {
            if (Param.group[i].is_auto_zoom != first_auto_zoom) {
                all_same_auto_zoom = false;
                break;
            }
        }
    }
    // 如果is_auto_zoom状态不相等，返回Zoom_Item_Count
    if (!all_same_auto_zoom) {
        return Zoom_Item_Count;
    }
    // 如果所有项都是自动变焦，返回FF
    if (first_auto_zoom) {
        return 0xFF;
    }
    // 检查zoom_level是否都相等
    uint8_t first_zoom = Param.group[0].zoom_level;
    LV_LOG("first zoom: %d\n", first_zoom);
    bool all_same_zoom = true;
    for (uint8_t i = 1; i < GroupItemCount; i++) {
        if (Param.group[i].zoom_level != first_zoom) {
            all_same_zoom = false;
            break;
        }
    }
    // 如果zoom_level都相等，返回zoom_level值，否则返回Zoom_Item_Count
    return all_same_zoom ? first_zoom : Zoom_Item_Count;
}

// 动画回调函数
// 修改图片的旋转角度
void rotate_img_anim_cb(void *img, int32_t angle) {
    // 设置图片的旋转角度
    // 角度精度为0.1度
    lv_img_set_angle(img, (int16_t) angle);
}

// 将整数数组转换为字符串数组
// 字符串长度最大为两位数加终止符，即3
void numbers_to_strings(uint8_t *numbers, int size, char output[CH_MAX][CH_Str_Length_Max]) {
    for (int i = 0; i < size; i++) {
        snprintf(output[i], 3, "%d", numbers[i]);
    }
}

// 选择扫描出来的频道回调
static void select_ch_cb(lv_event_t *e) {
    lv_obj_t *tar_obj = lv_event_get_target(e);
    lv_event_code_t code = lv_event_get_code(e);
    if (code == LV_EVENT_SHORT_CLICKED || code == LV_EVENT_KEY) {
        if (code == LV_EVENT_KEY) {
            char c = *((char *) lv_event_get_param(e));
            if (c == LV_KEY_HOME || c == LV_KEY_END) {
                page_click_anim(pos_left, &Pages.page[Page_Setting], anim_overlay);
            }
            if (c != LV_KEY_ENTER) {
                return;
            }
        }
        if (lv_obj_get_user_data(tar_obj) == NULL) {
            return;
        }
        uint8_t *user_data = lv_obj_get_user_data(tar_obj);
        uint8_t target_num_index = binary_search_index(ch_arr, CH_MAX, Setting.scan_ch_arr[*user_data]);
        lv_roller_set_selected(SettingObjs.roller[roller_setting_ch], target_num_index, LV_ANIM_ON);
        lv_event_send(SettingObjs.roller[roller_setting_ch], LV_EVENT_VALUE_CHANGED, NULL);
        lv_obj_del(Pages.ch_layout);
        Pages.ch_layout = NULL;
        // 刷新RF状态
        set_RF_status();
    }
}

void ch_layout_del_cb(lv_event_t *e) {
    lv_group_remove_all_objs(Pages.page[Page_Pop].indev_group);
    lv_group_del(Pages.page[Page_Pop].indev_group);
    Pages.page[Page_Pop].indev_group = NULL;
    // ch_layout存在状态下不选中直接退出页面须切到Pages.page[Page_Setting].indev_group
    if (PageManager.main_page == &Pages.page[Page_Setting_Detail]) {
        // 切换回Page_Setting_Detail页，并删除Page_Pop组
        set_indev_group(Pages.page[Page_Setting_Detail].indev_group);
    } else {
        set_indev_group(Pages.page[Page_Setting].indev_group);
    }
    // 避免没选择就退出,RF状态不正确
    set_RF_status();
}

// 频道扫描旋转动画完成回调
static void rotate_img_anim_ready_cb(lv_anim_t *e) {
    // 获取动画对象
    lv_obj_t *img = e->var;
    if (lv_obj_is_valid(Pages.ch_layout)) {
        lv_obj_del(Pages.ch_layout);
        Pages.ch_layout = NULL;
    }
    switch_group_handle(&Pages.page[Page_Pop]);
    // 覆盖于滚动选择器之上，选择后再销毁
    Pages.ch_layout = lv_layout_custom_create(Pages.page[Page_Setting_Detail].obj, LV_FLEX_FLOW_ROW_WRAP);
    lv_obj_add_flag(Pages.ch_layout, LV_OBJ_FLAG_IGNORE_LAYOUT);
    // 滚动选择器196高
    lv_obj_set_size(Pages.ch_layout, lv_obj_get_width(img->parent->parent->parent) - 18 * 2, 196);
    lv_obj_center(Pages.ch_layout);
    lv_obj_set_style_bg_color(Pages.ch_layout, lv_color_black(), 0);
    // lv_obj_set_style_pad_hor(Pages.ch_layout, 18, 0);
    // 行间距
    lv_obj_set_style_pad_column(Pages.ch_layout, 7, 0);
    // 列间距
    lv_obj_set_style_pad_row(Pages.ch_layout, 8, 0);
    lv_obj_add_event_cb(Pages.ch_layout, ch_layout_del_cb, LV_EVENT_DELETE, NULL);
#if !IS_REAL_DEVICE
    // for (int i = 0; i < 6; ++i) {
    //     Setting.scan_ch_arr[i] = i;
    // }
#endif
    char ch_str_arr[CH_MAX][CH_Str_Length_Max];
    // 调用将数字转换为字符串的函数
    numbers_to_strings(Setting.scan_ch_arr, CH_MAX, ch_str_arr);
    for (int i = 0; i < 6; ++i) {
        lv_obj_t *scan_ch_obj = lv_obj_create(Pages.ch_layout);
        lv_obj_set_size(scan_ch_obj, 106, 94);
        lv_obj_set_style_radius(scan_ch_obj, 32, 0);
        lv_obj_add_style(scan_ch_obj, &PageManager.default_style, 0);
        lv_obj_set_style_bg_color(scan_ch_obj, lv_color_hex(Bg_Color_Black), 0);
        lv_obj_t *ch_str = lv_label_custom_create(scan_ch_obj, ch_str_arr[i], FONT_SETTING_GRID, lv_color_white(),
                                                  LV_TEXT_ALIGN_CENTER);
        lv_obj_center(ch_str);
        static uint8_t user_data[6] = {0, 1, 2, 3, 4, 5};
        lv_obj_set_user_data(scan_ch_obj, (uint8_t *) &user_data[i]);
        lv_obj_add_event_cb(scan_ch_obj, select_ch_cb, LV_EVENT_ALL, NULL);
        border_focused_obj_init(scan_ch_obj, &Pages.page[Page_Pop], Focused_Opr_Parent);
    }
    lv_obj_del(img->parent->parent);
    SettingObjs.mask = NULL;
    // 手柄移出来便于拖动
    if (lv_obj_is_valid(handle_setting_detail_left)) {
        lv_obj_move_foreground(handle_setting_detail_left);
    } else {
        LV_LOG("handle_setting_detail_left is invalid\n");
    }
    defocused_handle(Pages.page[Page_Pop].indev_group);
    Setting.is_loading_mask_valid = false;
}

static void del_mask_cb(lv_anim_t *e) {
    // 获取动画对象
    lv_obj_t *img = e->var;
    lv_obj_del(img->parent->parent);
    SettingObjs.mask = NULL;

#if IS_REAL_DEVICE
    set_device_restart();
#endif
}

// 加载组件
// TODO 最好做成页面便于管理
void loading_obj_init(char str[20], bool is_repeat_infinite) {
    lv_obj_t *mask = lv_obj_custom_trans_create(lv_scr_act(), LV_PCT(100), LV_PCT(100));
    lv_obj_set_size(mask, LV_PCT(100), LV_PCT(100));
    lv_obj_set_style_bg_color(mask, lv_color_black(), 0);
    lv_obj_set_style_bg_opa(mask, LV_OPA_80, 0);
    SettingObjs.mask = mask;
    Setting.is_loading_mask_valid = true;

    lv_obj_t *layout = lv_layout_custom_create(mask, LV_FLEX_FLOW_COLUMN);
    lv_obj_set_flex_align(layout, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_set_style_bg_opa(layout, LV_OPA_TRANSP, 0);
    lv_obj_set_size(layout, LV_PCT(100), LV_PCT(100));
    lv_obj_set_style_pad_gap(layout, 40, 0);

    lv_obj_t *img = lv_img_custom_create(layout, &icon_loading);
    lv_obj_center(img);
    // lv_anim_t anim;
    lv_anim_init(&SettingObjs.rotate_img_anim);
    // 绑定需要进行动画的对象
    lv_anim_set_var(&SettingObjs.rotate_img_anim, img);
    // 设置旋转角度范围
    // 角度精度为0.1度
    // （360*10*3）表示转3圈
    lv_anim_set_values(&SettingObjs.rotate_img_anim, 0, 360 * 10 * 1);
    // 设置动画持续时间 ms
    lv_anim_set_time(&SettingObjs.rotate_img_anim, 1000);
    // 动画回调函数
    lv_anim_set_exec_cb(&SettingObjs.rotate_img_anim, rotate_img_anim_cb);
    if (!is_repeat_infinite) {
        lv_obj_t *label = lv_label_custom_create(layout, str, FONT_POP_WINDOW_INFO, lv_color_white(),
                                                 LV_TEXT_ALIGN_CENTER);
        // 动画完成回调函数
        lv_anim_set_ready_cb(&SettingObjs.rotate_img_anim, del_mask_cb);
    } else {
#if IS_REAL_DEVICE
        // 无限循环
        lv_anim_set_repeat_count(&SettingObjs.rotate_img_anim, LV_ANIM_REPEAT_INFINITE);
#endif
        // 动画完成回调函数
        lv_anim_set_ready_cb(&SettingObjs.rotate_img_anim, rotate_img_anim_ready_cb);
    }
    lv_anim_start(&SettingObjs.rotate_img_anim);
}

// 频道扫描按钮回调
static void scan_event_cb(lv_event_t *e) {
    // lv_obj_t *tar_obj = lv_event_get_target(e);
    lv_event_code_t code = lv_event_get_code(e);
    static bool is_short_clicked;
    static uint16_t count_pressing;
    if (code == LV_EVENT_PRESSING) {
        count_pressing++;
        if (count_pressing > Short_Click_Sensitivity) {
            is_short_clicked = false;
        } else {
            is_short_clicked = true;
        }
    } else if (code == LV_EVENT_RELEASED) {
        count_pressing = 0;
    } else if (code == LV_EVENT_SHORT_CLICKED || code == LV_EVENT_KEY) {
        if (code == LV_EVENT_KEY) {
            char c = *((char *) lv_event_get_param(e));
            if (c == LV_KEY_HOME || c == LV_KEY_END) {
                page_click_anim(pos_left, &Pages.page[Page_Setting], anim_overlay);
            }
            if (c != LV_KEY_ENTER) {
                return;
            }
        }
        if (is_short_clicked || code == LV_EVENT_KEY) {
            set_start_scan_ch();
            defocused_handle(Pages.page[Page_Setting_Detail].indev_group);
            if (SettingObjs.mask == NULL) {
                loading_obj_init(NULL, true);
            }
        }
    } else if (code == LV_EVENT_VALUE_CHANGED) {

    }
}

// 重置按钮回调
static void reset_btn_cb(lv_event_t *e) {
    lv_obj_t *tar_obj = lv_event_get_target(e);
    lv_event_code_t code = lv_event_get_code(e);
    char *str = lv_obj_get_user_data(tar_obj);
    // LV_LOG("%s\n", str);
    if (code == LV_EVENT_SHORT_CLICKED
        || (code == LV_EVENT_KEY && *((char *) lv_event_get_param(e)) == LV_KEY_ENTER)) {
        lv_obj_del(tar_obj->parent->parent);
        SettingObjs.mask = NULL;
        // set_reset();
        if (SettingObjs.mask == NULL) {
            loading_obj_init(str, false);
        }
    }
}

// 退出按钮回调
static void cancel_btn_cb(lv_event_t *e) {
    lv_obj_t *tar_obj = lv_event_get_target(e);
    lv_event_code_t code = lv_event_get_code(e);
    if (code == LV_EVENT_SHORT_CLICKED
        || (code == LV_EVENT_KEY && *((char *) lv_event_get_param(e)) == LV_KEY_ENTER)) {
        clean_group_obj_arr(&Pages.page[Page_Pop]);
        // 切换回Page_Setting_Detail页，并删除Page_Pop组
        set_indev_group(Pages.page[Page_Setting_Detail].indev_group);
        lv_group_remove_all_objs(Pages.page[Page_Pop].indev_group);
        lv_group_del(Pages.page[Page_Pop].indev_group);
        Pages.page[Page_Pop].indev_group = NULL;
        Param.is_wireless_sync = false;
        lv_obj_del(tar_obj->parent->parent);
        SettingObjs.mask = NULL;
        if (lv_obj_is_valid(Pages.ch_layout)) {
            lv_obj_del(Pages.ch_layout);
            Pages.ch_layout = NULL;
            set_RF_status();
        }
    }
}

// 无线同步按钮回调
static void pop_window_event_cb(lv_event_t *e) {
    // lv_obj_t *tar_obj = lv_event_get_target(e);
    lv_event_code_t code = lv_event_get_code(e);
    PopEnum pop_param = (PopEnum) (uintptr_t) lv_event_get_user_data(e);
    static bool is_short_clicked;
    static uint16_t count_pressing;
    if (code == LV_EVENT_PRESSING) {
        count_pressing++;
        if (count_pressing > Short_Click_Sensitivity) {
            is_short_clicked = false;
        } else {
            is_short_clicked = true;
        }
    } else if (code == LV_EVENT_RELEASED) {
        count_pressing = 0;
    } else if (code == LV_EVENT_SHORT_CLICKED || code == LV_EVENT_KEY) {
        if (code == LV_EVENT_KEY) {
            char c = *((char *) lv_event_get_param(e));
            if (c == LV_KEY_HOME) {
                page_click_anim(pos_left, &Pages.page[Page_Setting], anim_overlay);
            }
            if (c != LV_KEY_ENTER) {
                return;
            }
        }
        if (is_short_clicked || code == LV_EVENT_KEY) {
            // 解决设备组冲突
            // 创建ch_layout后点击无线同步
            if (lv_obj_is_valid(Pages.ch_layout)) {
                lv_obj_del(Pages.ch_layout);
                Pages.ch_layout = NULL;
                clean_group_obj_arr(&Pages.page[Page_Pop]);
            }
            // 切换到Pop页组
            switch_group_handle(&Pages.page[Page_Pop]);
            lv_obj_t *mask = lv_layout_custom_create(lv_scr_act(), LV_FLEX_FLOW_COLUMN);
            // 如果有遮罩了则不再创建
            if (SettingObjs.mask != NULL) {
                return;
            } else {
                SettingObjs.mask = mask;
            }
            lv_obj_add_flag(mask, LV_OBJ_FLAG_IGNORE_LAYOUT);
            lv_obj_set_size(mask, LV_PCT(100), LV_PCT(100));
            lv_obj_set_style_bg_color(mask, lv_color_black(), 0);
            lv_obj_set_style_pad_gap(mask, 0, 0);
            lv_obj_set_style_pad_hor(mask, 0, 0);
            lv_obj_set_flex_align(mask, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
            // 图标
            lv_obj_t *img;
            // 弹窗标题
            char title_str[30], info_str[50];
            if (pop_param == pop_sync_rf) {
                strncpy(title_str, get_text(text_wireless_sync), sizeof(title_str));
                strncpy(info_str, get_text(text_wireless_sync_info), sizeof(info_str));
                img = lv_img_custom_create(mask, &icon_synchronization);
                Param.is_wireless_sync = true;
            } else if (pop_param == pop_reset) {
                strncpy(title_str, get_text(text_factory_reset), sizeof(title_str));
                strncpy(info_str, get_text(text_factory_reset_warning), sizeof(info_str));
                img = lv_img_custom_create(mask, &icon_restore_factory_settings);
            }
            lv_obj_set_style_pad_top(img, 42, 0);
            title_str[sizeof(title_str) - 1] = '\0';
            lv_obj_t *label_title = lv_label_custom_create(mask, title_str, FONT_POP_WINDOW_TITLE,
                                                           lv_color_hex(Main_Color), LV_TEXT_ALIGN_CENTER);
            lv_obj_set_style_pad_top(label_title, 24, 0);
            // 弹窗提示信息
            lv_obj_t *label_info = lv_label_custom_create(mask, info_str,
                                                          FONT_POP_WINDOW_INFO, lv_color_white(), LV_TEXT_ALIGN_CENTER);
            // 自动换行
            lv_label_set_long_mode(label_info, LV_LABEL_LONG_WRAP);
            lv_obj_set_width(label_info, Screen_Width - 70);
            lv_obj_set_style_pad_top(label_info, 20, 0);
            lv_obj_set_style_pad_bottom(label_info, 46, 0);

            // 按钮布局
            lv_obj_t *h_layout = lv_layout_custom_create(mask, LV_FLEX_FLOW_ROW);
            lv_obj_set_size(h_layout, LV_PCT(100), 68);
            lv_obj_set_flex_align(h_layout, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
            lv_obj_set_style_bg_opa(h_layout, LV_OPA_TRANSP, 0);

            // 退出按钮
            lv_obj_t *cancel_button_obj = lv_obj_create(h_layout);
            lv_obj_set_size(cancel_button_obj, 168, 68);
            lv_obj_set_style_radius(cancel_button_obj, 79, 0);
            lv_obj_add_style(cancel_button_obj, &PageManager.default_style, 0);
            lv_obj_set_style_bg_color(cancel_button_obj, lv_color_hex(Bg_Color_Black), 0);
            lv_obj_t *label_button = lv_label_custom_create(cancel_button_obj, get_text(text_cancel),
                                                            FONT_BUTTON,
                                                            lv_color_white(),
                                                            LV_TEXT_ALIGN_CENTER);
            lv_obj_center(label_button);
            lv_obj_add_event_cb(cancel_button_obj, cancel_btn_cb, LV_EVENT_ALL, NULL);
            border_focused_obj_init(cancel_button_obj, &Pages.page[Page_Pop], Focused_Opr_Parent);

            if (pop_param == pop_reset) {
                // 确认按钮
                lv_obj_t *confirm_button_obj = lv_obj_create(h_layout);
                lv_obj_set_size(confirm_button_obj, 168, 68);
                lv_obj_set_style_radius(confirm_button_obj, 79, 0);
                lv_obj_add_style(confirm_button_obj, &PageManager.default_style, 0);
                lv_obj_set_style_bg_color(confirm_button_obj, lv_color_hex(Main_Color), 0);
                lv_obj_t *label_confirm_button = lv_label_custom_create(confirm_button_obj, get_text(text_confirm),
                                                                        FONT_BUTTON, lv_color_white(),
                                                                        LV_TEXT_ALIGN_CENTER);
                lv_obj_center(label_confirm_button);
                lv_obj_set_user_data(confirm_button_obj, get_text(text_factory_resetting));
                lv_obj_add_event_cb(confirm_button_obj, reset_btn_cb, LV_EVENT_ALL, NULL);

                lv_obj_set_style_text_opa(cancel_button_obj, MY_LABEL_OPA, 0);
                border_focused_obj_init(confirm_button_obj, &Pages.page[Page_Pop], Focused_Opr_Parent);
            }
            defocused_handle(Pages.page[Page_Pop].indev_group);
        }
    } else if (code == LV_EVENT_VALUE_CHANGED) {

    }
}

// 事件回调
static void sub_item_event_cb(lv_event_t *e) {
    lv_obj_t *tar_obj = lv_event_get_target(e);
    lv_event_code_t code = lv_event_get_code(e);
    SubItemStruct *sub_item_struct;
    SettingEnum setting_type;
    uint8_t own_id;
    // 防止 user_data 为空导致的死机
    if (lv_obj_get_user_data(tar_obj) != NULL) {
        sub_item_struct = lv_obj_get_user_data(tar_obj);
        setting_type = sub_item_struct->setting_type;
        own_id = sub_item_struct->own_id;
    } else {
        return;
    }
    if (code == LV_EVENT_SHORT_CLICKED || code == LV_EVENT_KEY) {
        if (code == LV_EVENT_KEY) {
            char c = *((char *) lv_event_get_param(e));
            if (c == LV_KEY_HOME) {
                page_click_anim(pos_left, &Pages.page[Page_Setting], anim_overlay);
            }
            if (c != LV_KEY_ENTER) {
                return;
            }
        }
        // Setting.values[setting_zoom]的值不更改，以实现每次进入群控焦距界面均为未选中状态
        // 利用 Setting.zoom 进行群控参数调节
        if (setting_type != setting_zoom) {
            Setting.values[setting_type] = own_id;
        } else {
            Setting.zoom = sub_item_struct->own_id;
            for (int i = 0; i < GroupItemCount; ++i) {
                // 如果处于自动焦距的状态则关闭自动再设值
                if (Param.group[i].is_auto_zoom) {
                    Param.group[i].is_auto_zoom = false;
                }
                Param.group[i].zoom_level = sub_item_struct->own_id;
            }
        }
        LV_LOG("Setting.values[%d] = %d\n", setting_type, Setting.values[setting_type]);
        lv_event_send(tar_obj, LV_EVENT_VALUE_CHANGED, NULL);
    } else if (code == LV_EVENT_VALUE_CHANGED) {
        LV_LOG("Setting.zoom: %d\n", Setting.zoom);
        if (Setting.zoom == zoom_auto && setting_type == setting_zoom) {
            own_id = zoom_auto;
        }

        // 遍历是否是选择的选项，不是则设为未选择的颜色，是则设为选择的颜色，如果背景obj为NULL则说明遍历结束了跳出遍历。
        for (int i = 0; i < Setting_Item_List_MAX; ++i) {
            if (SettingObjs.item[setting_type].sub_item[i].obj_bg != NULL) {
                if (i == own_id) {
                    // 选中
                    lv_obj_set_style_bg_color(SettingObjs.item[setting_type].sub_item[i].obj_bg,
                                              lv_color_hex(Main_Color), 0);
                    // 如果有图标区域，则图标区域的背景色也要改变
                    // 如果直接判断 SettingObjs.item[setting_type].sub_item[i].obj_title_bg != NULL 会死机
                    if (lv_obj_is_valid(SettingObjs.item[setting_type].sub_item[i].obj_title_bg)) {
                        lv_obj_set_style_bg_color(SettingObjs.item[setting_type].sub_item[i].obj_title_bg,
                                                  lv_color_hex(Main_Color_Opt), 0);
                        if (lv_obj_is_valid(SettingObjs.item[setting_type].sub_item[i].img_title)) {
                            lv_obj_set_style_img_opa(SettingObjs.item[setting_type].sub_item[i].img_title,
                                                     LV_OPA_COVER, 0);
                        } else {
                            lv_obj_set_style_text_opa(SettingObjs.item[setting_type].sub_item[i].label_title,
                                                      LV_OPA_COVER, 0);
                        }
                    }
                    if (lv_obj_is_valid(SettingObjs.item[setting_type].sub_item[i].img)) {
                        lv_obj_set_style_img_opa(SettingObjs.item[setting_type].sub_item[i].img, LV_OPA_COVER, 0);
                    } else {
                        lv_obj_set_style_text_opa(SettingObjs.item[setting_type].sub_item[i].label, LV_OPA_COVER, 0);
                    }
                    // 用于 zoom 页面下，点击了焦距后须关闭AUTO
                    if (setting_type == setting_zoom) {
                        if (lv_obj_is_valid(SettingObjs.item[setting_type].grid_title_bg)) {
                            lv_obj_set_style_bg_color(SettingObjs.item[setting_type].grid_title_bg,
                                                      lv_color_hex(Bg_Color_Gray), 0);
                        }
                        set_zoom_level(GroupItemCount);
                    }
                    // 修改能级显示模式时更新外部滑动条
                    // if (setting_type == setting_disp_type) {
                    //     for (int j = 0; j < GroupItemCount; ++j) {
                    //         if (lv_obj_is_valid(Pages.page[Page_Home].obj)) {
                    //             if (Param.group[j].is_added_to_list) {
                    //                 lv_event_send(Param.group[j].slider_home.slider_bg, LV_EVENT_VALUE_CHANGED, NULL);
                    //             }
                    //         } else if (lv_obj_is_valid(Pages.page[Page_Multi].obj)) {
                    //             lv_event_send(Param.multi.slider, LV_EVENT_VALUE_CHANGED, NULL);
                    //         }
                    //
                    //     }
                    // }
                } else {
                    // 未选中
                    lv_obj_set_style_bg_color(SettingObjs.item[setting_type].sub_item[i].obj_bg,
                                              lv_color_hex(Bg_Color_Black), 0);
                    if (lv_obj_is_valid(SettingObjs.item[setting_type].sub_item[i].obj_title_bg)) {
                        lv_obj_set_style_bg_color(SettingObjs.item[setting_type].sub_item[i].obj_title_bg,
                                                  lv_color_hex(Bg_Color_Gray), 0);
                        if (lv_obj_is_valid(SettingObjs.item[setting_type].sub_item[i].img_title)) {
                            lv_obj_set_style_img_opa(SettingObjs.item[setting_type].sub_item[i].img_title,
                                                     MY_IMG_OPA, 0);
                        } else {
                            lv_obj_set_style_text_opa(SettingObjs.item[setting_type].sub_item[i].label_title,
                                                      MY_LABEL_OPA, 0);
                        }
                    }
                    if (lv_obj_is_valid(SettingObjs.item[setting_type].sub_item[i].img)) {
                        lv_obj_set_style_img_opa(SettingObjs.item[setting_type].sub_item[i].img, MY_IMG_OPA, 0);
                    } else {
                        lv_obj_set_style_text_opa(SettingObjs.item[setting_type].sub_item[i].label, MY_LABEL_OPA, 0);
                    }
                }
            } else {
                break;
            }
        }
        // 具体业务操作
        switch (setting_type) {
            case setting_rf:
                break;
            case setting_dist:
                set_RF_status();
                break;
#if ProductModel == QZ_N
                case setting_sync:
                    Setting.last_sync_value = Setting.values[setting_sync];
                    break;
#elif ProductModel == QZ_F
            case setting_zoom_disp:
                if (lv_obj_is_valid(Pages.page[Page_Group_Info].obj)) {
                    for (int i = 0; i < GroupItemCount; ++i) {
                        if (Param.group[i].is_added_to_list) {
                            lv_event_send(Param.group[i].slider_option_zoom_level.slider_bg, LV_EVENT_VALUE_CHANGED,
                                          NULL);
                        }
                    }
                }
                break;
#endif
            case setting_beep:
            case setting_zoom:
                // 更新Page_Group_Info页的slider_option_zoom_level滑动条
                if (Pages.page[Page_Group_Info].state == page_created) {
                    for (int i = 0; i < GroupItemCount; ++i) {
                        lv_event_send(Param.group[i].slider_option_zoom_level.slider_bg, LV_EVENT_VALUE_CHANGED,
                                      NULL);
                    }
                }
                set_beep_lamp_level();
                break;
                //case setting_2_5mm:
                //    break;
            case setting_screen:
                break;
            case setting_disp_type:
                break;
            case setting_language:
                // 修改被覆盖的页面
                if (lv_obj_is_valid(Pages.page[Page_Home].obj)) {
                    for (int i = 0; i < GroupItemCount; ++i) {
                        if (Param.group[i].is_added_to_list) {
                            lv_event_send(Param.group[i].slider_home.slider_bg, LV_EVENT_VALUE_CHANGED, NULL);
                        }
                    }
                    lv_event_send(Param.more_label_obj, LV_EVENT_VALUE_CHANGED, NULL);
                } else if (lv_obj_is_valid(Pages.page[Page_Group_Info].obj)) {
                    for (int i = 0; i < GroupItemCount; ++i) {
                        if (Param.group[i].is_added_to_list) {
                            lv_event_send(Param.group[i].slider_option_lamp_level.slider_bg, LV_EVENT_VALUE_CHANGED,
                                          NULL);
                            lv_event_send(Param.group[i].slider_option_zoom_level.slider_bg, LV_EVENT_VALUE_CHANGED,
                                          NULL);
                        }
                    }
                }
                lv_event_send(SettingObjs.item[setting_language].grid_title_bg, LV_EVENT_VALUE_CHANGED, NULL);
                break;
            case setting_reset:
                break;
            case setting_shoot:
                break;
            case setting_tcm:
                break;
#if MODULE_BT
                case setting_bluetooth:
                    break;
#endif
            default:
                break;
        }
    }
}

// 列表项（带描述的选项）初始化
SubItemStruct setting_list_items(lv_obj_t *parent, const void *img_src, char *opt_str, SubItemConfig *item_config,
                                 SettingEnum selected_item, uint8_t own_id) {
    SubItemStruct list_item;

    // 获取文本，如果需要翻译则调用get_text
    char *display_text;
    if (item_config->need_translate) {
        display_text = get_text(item_config->text_id);
    } else {
        display_text = item_config->text;
    }

    lv_obj_t *h_layout = lv_layout_custom_create(parent, LV_FLEX_FLOW_ROW);
    lv_obj_set_style_pad_gap(h_layout, 0, 0);
    lv_obj_set_style_radius(h_layout, RADIUS_DEFAULT, 0);
    lv_obj_set_style_bg_color(h_layout, lv_color_hex(Bg_Color_Black), 0);
    // lv_obj_set_style_border_width(h_layout, 2, 0);
    const uint8_t icon_area_h = 100;
    lv_obj_set_size(h_layout, LV_PCT(100), icon_area_h);
    // user_data是void * user_data类型，即只能传指针（数组是指针）
    // 使用 & 来获取 SettingObjs.item[selected_item].sub_item[own_id] 的地址
    lv_obj_set_user_data(h_layout, &SettingObjs.item[selected_item].sub_item[own_id]);
    lv_obj_add_event_cb(h_layout, sub_item_event_cb, LV_EVENT_ALL, NULL);

    // 前方标题图标区域
    lv_obj_t *title_area = lv_obj_create(h_layout);
    lv_obj_set_style_bg_color(title_area, lv_color_hex(Bg_Color_Gray), 0);
    lv_obj_set_style_radius(title_area, RADIUS_DEFAULT, 0);
    lv_obj_set_size(title_area, icon_area_h, icon_area_h);
    lv_obj_add_style(title_area, &PageManager.default_style, 0);
    lv_obj_t *opt_label = NULL, *opt_icon = NULL;
    if (img_src == NULL) {
        opt_label = lv_label_custom_create(title_area, opt_str, FONT_SETTING, lv_color_white(), LV_TEXT_ALIGN_CENTER);
        lv_obj_center(opt_label);
    } else {
        opt_icon = lv_img_custom_create(title_area, img_src);
        lv_obj_set_style_img_opa(opt_icon, MY_IMG_OPA, 0);
        lv_obj_center(opt_icon);
    }
    // 清除可点击标志，让整个条可触发同一个点击事件
    lv_obj_clear_flag(title_area, LV_OBJ_FLAG_CLICKABLE);

    // 更新视图以获取h_layout的宽度，否则会获取到0
    lv_obj_update_layout(h_layout);
    lv_obj_t *opt_text_area = lv_obj_create(h_layout);
    lv_obj_set_size(opt_text_area, lv_obj_get_width(h_layout) - icon_area_h, icon_area_h);
    lv_obj_add_style(opt_text_area, &PageManager.default_style, 0);
    lv_obj_set_style_bg_opa(opt_text_area, LV_OPA_TRANSP, 0);
    // 清除可点击标志，让整个条可触发同一个点击事件
    lv_obj_clear_flag(opt_text_area, LV_OBJ_FLAG_CLICKABLE);
    lv_obj_t *label = lv_label_custom_create(opt_text_area, display_text, FONT_SETTING, lv_color_white(),
                                             LV_TEXT_ALIGN_CENTER);
    lv_obj_center(label);
    lv_obj_set_style_text_opa(label, MY_LABEL_OPA, 0);
    // 是否为当前选择项，是则修改背景色
    if (Setting.values[selected_item] == own_id) {
        lv_obj_set_style_bg_color(h_layout, lv_color_hex(Main_Color), 0);
        lv_obj_set_style_bg_color(title_area, lv_color_hex(Main_Color_Opt), 0);
        lv_obj_set_style_text_opa(label, LV_OPA_COVER, 0);
        if (lv_obj_is_valid(opt_label)) {
            lv_obj_set_style_text_opa(opt_label, LV_OPA_COVER, 0);
        } else {
            lv_obj_set_style_img_opa(opt_icon, LV_OPA_COVER, 0);
        }
    }

    border_focused_obj_init(h_layout, &Pages.page[Page_Setting_Detail], Focused_Opr_Parent);

    list_item.setting_type = selected_item;
    list_item.own_id = own_id;
    list_item.obj_bg = h_layout;
    list_item.obj_title_bg = title_area;
    list_item.label_title = opt_label;
    list_item.img_title = opt_icon;
    list_item.label = label;
    // TODO 正文区域显示图标未编写
    list_item.img = NULL;

    return list_item;
}

// 短选项（不带描述的选项）初始化
SubItemStruct setting_items(lv_obj_t *parent, ItemTypeEnum item_type, const void *img_src, SubItemConfig *item_config,
                            SettingEnum selected_item, uint8_t own_id) {
    SubItemStruct list_item;

    // 获取文本，如果需要翻译则调用get_text
    char *display_text;
    if (item_config->need_translate) {
        display_text = get_text(item_config->text_id);
    } else {
        display_text = item_config->text;
    }

    const lv_font_t *font;
    lv_coord_t obj_w, obj_h;
    if (item_type == item_grid) {
        obj_w = 106;
        obj_h = 90;
        font = FONT_SETTING_GRID;
    } else {
        obj_w = 162;
        obj_h = 116;
        font = FONT_SETTING_ITEM;
    }

    lv_obj_t *bg = lv_obj_create(parent);
    lv_obj_add_style(bg, &PageManager.default_style, 0);
    lv_obj_set_size(bg, obj_w, obj_h);
    lv_obj_set_style_radius(bg, RADIUS_DEFAULT, 0);
    lv_obj_set_style_bg_color(bg, lv_color_hex(Bg_Color_Black), 0);
    lv_obj_clear_flag(bg, LV_OBJ_FLAG_SCROLLABLE);
    lv_obj_set_scrollbar_mode(bg, LV_SCROLLBAR_MODE_OFF);
    lv_obj_set_user_data(bg, &SettingObjs.item[selected_item].sub_item[own_id]);
    lv_obj_add_event_cb(bg, sub_item_event_cb, LV_EVENT_ALL, NULL);
    lv_obj_t *label = NULL, *icon = NULL;
    if (img_src == NULL) {
        label = lv_label_custom_create(bg, display_text, font, lv_color_white(), LV_TEXT_ALIGN_CENTER);
        lv_obj_set_style_text_opa(label, MY_LABEL_OPA, 0);
        lv_obj_center(label);
    } else {
        icon = lv_img_custom_create(bg, img_src);
        lv_obj_set_style_img_opa(icon, MY_IMG_OPA, 0);
        lv_obj_center(icon);
    }
    border_focused_obj_init(bg, &Pages.page[Page_Setting_Detail], Focused_Opr_Parent | Focused_Scroll_Parent);

    // 是否为当前选择项，是则修改背景色
    if (Setting.values[selected_item] == own_id) {
        lv_obj_set_style_bg_color(bg, lv_color_hex(Main_Color), 0);
        if (lv_obj_is_valid(icon)) {
            lv_obj_set_style_img_opa(icon, LV_OPA_COVER, 0);
        } else {
            lv_obj_set_style_text_opa(label, LV_OPA_COVER, 0);
        }
    }

    list_item.setting_type = selected_item;
    list_item.own_id = own_id;
    list_item.obj_bg = bg;
    // 不赋值NULL会偶发导致操作未知对象
    list_item.obj_title_bg = NULL;
    list_item.label_title = NULL;
    list_item.img_title = NULL;
    // 空的list_item.img 和 list_item.label若没有初始化为NULL判断可能会有问题
    list_item.img = icon;
    list_item.label = label;

    return list_item;
}

// 设置项配置
SubItemConfig *conf_setting[] = {
        // RF 滚动选择器页
        NULL,
        // DIST
        (SubItemConfig[]) {
                {item_list, &icon_range_close, "", .text = "0-10m",  dist_0_10,  false},
                {item_list, &icon_range_long,  "", .text = "1-100m", dist_1_100, false}
        },
#if ProductModel == QZ_N
        // SYNC
        (SubItemConfig[]) {
                {item_list, &icon_front_curtain, "", .text_id = text_front_curtain_sync, sync_front,      true},
// #if ProductModel != QZ_N
//                 {item_list, &icon_rear_curtain,  "", .text_id = text_rear_curtain_sync,  sync_rear, true},
// #endif
                {item_list, &icon_high_speed,    "", .text_id = text_high_speed_sync,    sync_high_speed, true}
        },
#elif ProductModel == QZ_F
        (SubItemConfig[]) {
                {item_normal, NULL, "", .text = "APS",   zoom_aps, false},
                {item_normal, NULL, "", .text = "135mm", zoom_135, false}
        },
#endif
        // BEEP
        (SubItemConfig[]) {
                {item_normal, &icon_vol_on,  "", .text = "", beep_on,  false},
                {item_normal, &icon_vol_off, "", .text = "", beep_off, false}
        },
        // ZOOM
        (SubItemConfig[]) {
                {item_grid, NULL, "", .text = "20mm",  0, false},
                {item_grid, NULL, "", .text = "24mm",  1, false},
                {item_grid, NULL, "", .text = "28mm",  2, false},
                {item_grid, NULL, "", .text = "35mm",  3, false},
                {item_grid, NULL, "", .text = "50mm",  4, false},
                {item_grid, NULL, "", .text = "70mm",  5, false},
                {item_grid, NULL, "", .text = "80mm",  6, false},
                {item_grid, NULL, "", .text = "105mm", 7, false},
                {item_grid, NULL, "", .text = "135mm", 8, false},
                {item_grid, NULL, "", .text = "200mm", 9, false},
        },
        // TCM 开关
        NULL,
        // SHOOT(单触点模式，模式和频闪禁用)
        NULL,
        // PC同步口
        // (SubItemConfig[]) {
        //         {item_normal, NULL, "", "IN",  pc_in},
        //         {item_normal, NULL, "", "OUT", pc_out}
        // },
        // SCREEN 滚动选择器页
        NULL,
        // Bluetooth 开关
#if MODULE_BT
        NULL,
#endif
        // DISP显示模式
        // (SubItemConfig[]) {
        //         {item_list, &icon_fractions, "", "Fractions", disp_fractions},
        //         {item_list, &icon_decimals,  "", "Decimals",  disp_decimals}
        // },
        NULL,
        // LANGUAGE 多语言
        (SubItemConfig[]) {
                {item_grid, NULL, "", "CH", lang_ch, false},
                {item_grid, NULL, "", "EN", lang_en, false},
                // {item_grid, NULL, "", "DE", lang_de, false},
                // {item_grid, NULL, "", "ES", lang_es, false},
                // {item_grid, NULL, "", "IT", lang_it, false},
                // {item_grid, NULL, "", "FR", lang_fr, false},
                // {item_grid, NULL, "", "RU", lang_ru, false},
                // {item_grid, NULL, "", "NE", lang_ne, false},
                // {item_grid, NULL, "", "JP", lang_jp, false},
        },
        // RESET 特殊页
        NULL,
#if ProductModel == QZ_F
        NULL,
        (SubItemConfig[]) {
                {item_grid, NULL, "", .text = "13mm",  0, false},
                {item_grid, NULL, "", .text = "16mm",  1, false},
                {item_grid, NULL, "", .text = "18mm",  2, false},
                {item_grid, NULL, "", .text = "23mm",  3, false},
                {item_grid, NULL, "", .text = "33mm",  4, false},
                {item_grid, NULL, "", .text = "46mm",  5, false},
                {item_grid, NULL, "", .text = "53mm",  6, false},
                {item_grid, NULL, "", .text = "69mm",  7, false},
                {item_grid, NULL, "", .text = "88mm",  8, false},
                {item_grid, NULL, "", .text = "133mm", 9, false},
        },
#endif
};

// 创建设置页子项
void create_sub_items(lv_obj_t *parent, SettingEnum setting_type, const SubItemConfig *config,
                      uint8_t config_size) {
    for (int i = 0; i < config_size; i++) {
        const SubItemConfig *item = &config[i];
        if (item->item_type == item_normal || item->item_type == item_grid) {
            SettingObjs.item[setting_type].sub_item[item->type] = setting_items(
                    parent, item->item_type, item->icon, (SubItemConfig *) item, setting_type, item->type
            );
        } else {
            SettingObjs.item[setting_type].sub_item[item->type] = setting_list_items(
                    parent, item->icon, item->title, (SubItemConfig *) item, setting_type, item->type
            );
        }
    }
}

// 网格布局标题
lv_obj_t *setting_grid_title(lv_obj_t *parent, char *str, lv_color_t bg_color, const lv_font_t *font) {
    lv_obj_t *bg = lv_obj_create(parent);
    lv_obj_add_style(bg, &PageManager.default_style, 0);
    lv_obj_set_size(bg, LV_PCT(100), 100);
    lv_obj_set_style_radius(bg, 50, 0);
    lv_obj_set_style_bg_color(bg, bg_color, 0);
    lv_obj_t *label = lv_label_custom_create(bg, str, font, lv_color_white(), LV_TEXT_ALIGN_CENTER);
    lv_obj_center(label);
    lv_obj_add_event_cb(bg, setting_grid_title_cb, LV_EVENT_VALUE_CHANGED, NULL);
    SettingObjs.item[Setting.selected_item].grid_title_bg = bg;
    return bg;
}

// 设置页滚动选择器回调
static void roller_event_handler(lv_event_t *e) {
    lv_event_code_t code = lv_event_get_code(e);
    if (code == LV_EVENT_VALUE_CHANGED) {
        lv_obj_t *roller = lv_event_get_target(e);
        uint8_t selected_id = lv_roller_get_selected(roller);
        // (uintptr_t)将整数类型强制转换为 void*
        RollerSettingTypeEnum roller_type = (RollerSettingTypeEnum) (uintptr_t) lv_obj_get_user_data(roller);
        LV_LOG("Setting.roller_values[%d] = %d\n", roller_type, selected_id);
        Setting.roller_values[roller_type] = selected_id;
        // 修改功率需更新外部滑条
        if (roller_type == roller_min_power || roller_type == roller_power_step) {
            // if (roller_type == roller_min_power)
            int8_t data_now = max_level[Setting.roller_values[roller_min_power] % MAX_FLASH_LEVEL_INDEX];
            int8_t data_last = max_level[Setting.last_min_power % MAX_FLASH_LEVEL_INDEX];

            int8_t difference = data_now - data_last;
            int8_t multi_difference = difference / 10;
            if (roller_type == roller_min_power) {
                if (Setting.last_min_power % MAX_FLASH_LEVEL_INDEX >
                    Setting.roller_values[roller_min_power] % MAX_FLASH_LEVEL_INDEX) {
                    if (Param.multi.flash_level > LV_ABS(multi_difference)) {
                        Param.multi.flash_level = Param.multi.flash_level + multi_difference;
                    } else {
                        Param.multi.flash_level = 0;
                    }
                } else {
                    Param.multi.flash_level = Param.multi.flash_level + multi_difference;
                }
            }
            for (int j = 0; j < GroupItemCount; ++j) {
                if (roller_type == roller_power_step) {
                    // 当设置为 step 0.3 时将所有M进位
                    if (selected_id == step_0_3) {
                        Param.group[j].flash_level_M = level_step_0_3_handle(1, Param.group[j].flash_level_M);
                    } else {
                        break;
                    }
                } else {
                    // 由于能级最小值为0，修改最小能级后须校正当前能级
                    if (Setting.last_min_power % MAX_FLASH_LEVEL_INDEX >
                        Setting.roller_values[roller_min_power] % MAX_FLASH_LEVEL_INDEX) {
                        if (Param.group[j].flash_level_M > LV_ABS(difference)) {
                            Param.group[j].flash_level_M = Param.group[j].flash_level_M + difference;
                        } else {
                            // 当前值小于差值的绝对值时，也就是能级范围缩小后该值小于0，则校正为0
                            // 例如能级为1/512，最小能级改为1/128后，1/512已经被截取，则置为0也就是1/128
                            Param.group[j].flash_level_M = 0;
                        }
                    } else {
                        Param.group[j].flash_level_M = Param.group[j].flash_level_M + difference;
                    }
                }
                if (Param.group[j].mode == mode_M) {
                    if (lv_obj_is_valid(Pages.page[Page_Home].obj)) {
                        if (Param.group[j].is_added_to_list) {
                            lv_event_send(Param.group[j].slider_home.slider_bg, LV_EVENT_VALUE_CHANGED, NULL);
                        }
                    } else if (lv_obj_is_valid(Pages.page[Page_Multi].obj)) {
                        lv_event_send(Param.multi.slider, LV_EVENT_VALUE_CHANGED, NULL);
                    } else if (lv_obj_is_valid(Pages.page[Page_Group_Info].obj)) {
                        if (Param.group[j].is_added_to_list) {
                            lv_event_send(Param.group[j].slider_option_flash_level.slider_bg, LV_EVENT_VALUE_CHANGED,
                                          NULL);
                        }
                    }
                }
            }
            Setting.last_min_power = Setting.roller_values[roller_min_power];
        }
    } else if (code == LV_EVENT_KEY) {
        char c = *((char *) lv_event_get_param(e));
        if (c == LV_KEY_HOME || c == LV_KEY_END) {
            page_click_anim(pos_left, &Pages.page[Page_Setting], anim_overlay);
        }
    }
}

// 滚动选择器初始化
void setting_roller_init(lv_obj_t *parent, char *title_str, char *option_str, RollerSettingTypeEnum roller_type) {
    const uint8_t radius = 32;
    const uint8_t roller_title_h = 48;
    uint8_t roller_h = 148;
    uint8_t roller_text_line_space = 25;
    if (roller_type == roller_min_power || roller_type == roller_power_step) {
        roller_h = 234;
        roller_text_line_space = 20;
    }
    const uint16_t roller_container_h = roller_title_h + roller_h;
    // 滚动选择器容器
    lv_obj_t *roller_container = lv_layout_custom_create(parent, LV_FLEX_FLOW_COLUMN);
    lv_obj_set_style_bg_color(roller_container, lv_color_hex(Bg_Color_Black), 0);
    lv_obj_set_size(roller_container, 160, roller_container_h);
    lv_obj_set_style_radius(roller_container, radius, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_clip_corner(roller_container, true, LV_PART_MAIN | LV_STATE_DEFAULT);
    // lv_obj_set_style_pad_top(roller_container, 4, 0);
    lv_obj_set_style_pad_gap(roller_container, 0, 0);
    lv_obj_clear_flag(roller_container, LV_OBJ_FLAG_SCROLLABLE);
    lv_obj_set_flex_align(roller_container, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    // lv_obj_set_style_border_width(roller_container, 1, 0);
    // lv_obj_set_style_border_color(roller_container, lv_color_white(), 0);

    // 滚动选择器标题
    lv_obj_t *title_obj = lv_obj_create(roller_container);
    lv_obj_add_style(title_obj, &PageManager.default_style, 0);
    lv_obj_set_size(title_obj, LV_PCT(100), roller_title_h);
    lv_obj_set_style_bg_color(title_obj, lv_color_hex(Bg_Color_Gray), 0);
    lv_obj_t *title_label = lv_label_custom_create(title_obj, title_str, FONT_SETTING_ROLLER_TITLE, lv_color_white(),
                                                   LV_TEXT_ALIGN_CENTER);
    lv_obj_center(title_label);

    // 滚动选择器
    lv_obj_t *roller = lv_roller_create(roller_container);
    lv_obj_set_style_text_line_space(roller, roller_text_line_space, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(roller, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_roller_set_options(roller, option_str, LV_ROLLER_MODE_NORMAL);
    lv_obj_set_size(roller, LV_PCT(100), roller_h);
    lv_obj_set_style_bg_opa(roller, LV_OPA_TRANSP, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(roller, lv_color_white(), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(roller, LV_OPA_40, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(roller, FONT_SETTING_ROLLER_UNSELECTED, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_set_style_bg_opa(roller, LV_OPA_TRANSP, LV_PART_SELECTED | LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(roller, lv_color_white(), LV_PART_SELECTED | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(roller, FONT_SETTING_ROLLER_SELECTED, LV_PART_SELECTED | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(roller, LV_OPA_COVER, LV_PART_SELECTED | LV_STATE_DEFAULT);
    lv_roller_set_selected(roller, Setting.roller_values[roller_type], LV_ANIM_OFF);
    // 添加事件回调
    lv_obj_add_event_cb(roller, roller_event_handler, LV_EVENT_ALL, NULL);
    // (uintptr_t)将整数类型强制转换为 void*
    // 以便将单个变量作为user_data
    lv_obj_set_user_data(roller, (void *) (uintptr_t) roller_type);
    SettingObjs.roller[roller_type] = roller;

    border_focused_obj_init(roller_container, &Pages.page[Page_Setting_Detail], Focused_Editing_Mode);
    // roller会自动添加到默认组，从默认组移除roller，以解决操作roller后返回上一页会自动聚焦第一个对象的问题
    lv_group_remove_obj(roller);
}

// 列表布局
void setting_list_layout(lv_obj_t *parent) {
    lv_obj_t *v_layout = lv_layout_custom_create(parent, LV_FLEX_FLOW_ROW_WRAP);
    const uint8_t grid_pad_top = 14;
    lv_obj_set_size(v_layout, LV_PCT(100), LV_PCT(100));
    lv_obj_set_style_bg_opa(v_layout, LV_OPA_TRANSP, 0);
    lv_obj_set_style_pad_hor(v_layout, 18, 0);
    lv_obj_set_style_pad_top(v_layout, status_bar_h + 10, 0);
    lv_obj_set_scrollbar_mode(v_layout, LV_SCROLLBAR_MODE_ACTIVE);
    if (Setting.selected_item == setting_dist) {
        // 列表
        lv_obj_set_style_pad_gap(v_layout, 10, 0);
        create_sub_items(v_layout, Setting.selected_item, conf_setting[Setting.selected_item], Dist_Item_Count);
        lv_obj_t *label_info = lv_label_custom_create(v_layout,
                                                      get_text(text_distance_warning),
                                                      FONT_POP_WINDOW_INFO, lv_color_hex(Setting_Info_Color),
                                                      LV_TEXT_ALIGN_LEFT);
        lv_obj_set_width(label_info, Screen_Width - 18 * 2);
        lv_label_set_long_mode(label_info, LV_LABEL_LONG_WRAP);
        lv_obj_add_flag(label_info, LV_OBJ_FLAG_IGNORE_LAYOUT);
        lv_obj_set_style_pad_top(label_info, Screen_Width / 4 * 3, 0);
    } else
#if ProductModel == QZ_N
        if (Setting.selected_item == setting_sync) {
        // 同步方式
        // 列表
        lv_obj_set_style_pad_gap(v_layout, 10, 0);
        create_sub_items(v_layout, Setting.selected_item, conf_setting[Setting.selected_item], Sync_Item_Count);
    }
#elif ProductModel == QZ_F
    if (Setting.selected_item == setting_zoom_disp) {
        // 同步方式
        // 列表
        lv_obj_set_style_pad_gap(v_layout, 8, 0);
        create_sub_items(v_layout, Setting.selected_item, conf_setting[Setting.selected_item], Zoom_Disp_Item_Count);
    }
#endif
    else if (Setting.selected_item == setting_disp_type) {
        // 滚动选择器
        lv_obj_set_style_pad_gap(v_layout, 12, 0);
        // create_sub_items(v_layout, Setting.selected_item, conf_setting[Setting.selected_item], Disp_Item_Count);
        lv_obj_set_flex_align(v_layout, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
        lv_obj_set_style_pad_top(v_layout, 0, 0);
        char *min_power_str = "1/128\n1/256\n1/512\n3.0\n2.0\n1.0";
        char *step_str = "+0.1\n+0.3";
        setting_roller_init(v_layout, get_text(text_min_power), min_power_str, roller_min_power);
        setting_roller_init(v_layout, get_text(text_step), step_str, roller_power_step);
    } else if (Setting.selected_item == setting_beep) {
        // 选项
        lv_obj_set_style_pad_gap(v_layout, 8, 0);
        create_sub_items(v_layout, Setting.selected_item, conf_setting[Setting.selected_item], Beep_Item_Count);
        lv_obj_t *label_info = lv_label_custom_create(v_layout,
                                                      get_text(text_beep_info),
                                                      FONT_POP_WINDOW_INFO, lv_color_hex(Setting_Info_Color),
                                                      LV_TEXT_ALIGN_LEFT);
        lv_obj_set_width(label_info, Screen_Width - 18 * 2);
        lv_label_set_long_mode(label_info, LV_LABEL_LONG_WRAP);
        lv_obj_add_flag(label_info, LV_OBJ_FLAG_IGNORE_LAYOUT);
        lv_obj_set_style_pad_top(label_info, Screen_Width / 4 * 3, 0);
    }
        // else if (Setting.selected_item == setting_2_5mm) {
        //     // 选项
        //     lv_obj_set_style_pad_gap(v_layout, 8, 0);
        //     create_sub_items(v_layout, Setting.selected_item, conf_setting[Setting.selected_item], PC_Item_Count);
        // }
    else if (Setting.selected_item == setting_zoom) {
        // ZOOM 九宫格
        lv_obj_t *auto_obj = setting_grid_title(v_layout, get_text(text_auto), lv_color_hex(Bg_Color_Gray),
                                                FONT_SETTING);
        lv_obj_add_event_cb(auto_obj, auto_event_cb, LV_EVENT_ALL, NULL);
        border_focused_obj_init(auto_obj, &Pages.page[Page_Setting_Detail], Focused_Opr_Parent | Focused_Scroll_Parent);
        lv_obj_set_style_pad_top(v_layout, grid_pad_top, 0);
        lv_obj_set_style_pad_column(v_layout, 7, 0);
        lv_obj_set_style_pad_row(v_layout, 12, 0);
        // 检测各组zoom状态
        Setting.values[setting_zoom] = check_group_zoom_status();
        if (Setting.values[setting_zoom] == zoom_auto) {
            lv_obj_set_style_bg_color(auto_obj, lv_color_hex(Main_Color), 0);
        }
        // 超过九宫格就底部加边距
        if (Zoom_Item_Count > 9) {
            lv_obj_set_style_pad_bottom(v_layout, grid_pad_top + 10, 0);
        }
        // 设置滚动方向，便于焦点判断
        lv_obj_set_scroll_dir(v_layout, LV_DIR_VER);
#if ProductModel == QZ_N
        create_sub_items(v_layout, Setting.selected_item, conf_setting[Setting.selected_item], Zoom_Item_Count);
#elif ProductModel == QZ_F
        if (Setting.values[setting_zoom_disp] == zoom_aps) {
            create_sub_items(v_layout, Setting.selected_item, conf_setting[setting_zoom_aps], Zoom_Item_Count);
        } else {
            create_sub_items(v_layout, Setting.selected_item, conf_setting[Setting.selected_item], Zoom_Item_Count);
        }
#endif

    } else if (Setting.selected_item == setting_language) {
        // 九宫格
        lv_obj_set_style_pad_top(v_layout, grid_pad_top, 0);
        setting_grid_title(v_layout, get_text(text_language), lv_color_black(), FONT_SETTING);
        lv_obj_set_style_pad_column(v_layout, 7, 0);
        lv_obj_set_style_pad_row(v_layout, 12, 0);
        create_sub_items(v_layout, Setting.selected_item, conf_setting[Setting.selected_item], Language_Item_Count);
    } else if (Setting.selected_item == setting_rf) {
        // 带滚动选择器
        lv_obj_set_style_pad_top(v_layout, grid_pad_top, 0);
        lv_obj_set_style_pad_gap(v_layout, 12, 0);
        lv_obj_t *scan_obj = setting_grid_title(v_layout, get_text(text_scan_channel), lv_color_hex(Bg_Color_Black),
                                                FONT_SETTING_ROLLER_TITLE);
        lv_obj_add_event_cb(scan_obj, scan_event_cb, LV_EVENT_ALL, NULL);
        border_focused_obj_init(scan_obj, &Pages.page[Page_Setting_Detail], Focused_Opr_Parent);

        char *ch_option_str = "1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n13\n14\n15\n16\n17\n18\n19\n20\n"
                              "21\n22\n23\n24\n25\n26\n27\n28\n29\n30\n31\n32";
        char *id_option_str = "OFF\n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n13\n14\n15\n16\n17\n18\n19\n20\n"
                              "21\n22\n23\n24\n25\n26\n27\n28\n29\n30\n31\n32\n33\n34\n35\n36\n37\n38\n39\n40\n"
                              "41\n42\n43\n44\n45\n46\n47\n48\n49\n50\n51\n52\n53\n54\n55\n56\n57\n58\n59\n60\n"
                              "61\n62\n63\n64\n65\n66\n67\n68\n69\n70\n71\n72\n73\n74\n75\n76\n77\n78\n79\n80\n"
                              "81\n82\n83\n84\n85\n86\n87\n88\n89\n90\n91\n92\n93\n94\n95\n96\n97\n98\n99";
        setting_roller_init(v_layout, get_text(text_channel), ch_option_str, roller_setting_ch);
        setting_roller_init(v_layout, get_text(text_id), id_option_str, roller_setting_id);
        lv_obj_t *rf_sync_obj = setting_grid_title(v_layout, get_text(text_wireless_sync), lv_color_hex(Bg_Color_Black),
                                                   FONT_SETTING_ROLLER_TITLE);
        uint8_t pop_type = pop_sync_rf;
        lv_obj_add_event_cb(rf_sync_obj, pop_window_event_cb, LV_EVENT_ALL, (void *) (uintptr_t) pop_type);
        border_focused_obj_init(rf_sync_obj, &Pages.page[Page_Setting_Detail], Focused_Opr_Parent);

    } else if (Setting.selected_item == setting_screen) {
        // 带滚动选择器
        lv_obj_set_style_pad_top(v_layout, grid_pad_top, 0);
        lv_obj_set_style_pad_column(v_layout, 12, 0);
        lv_obj_set_style_pad_row(v_layout, 12, 0);

        lv_obj_t *container = lv_layout_custom_create(v_layout, LV_FLEX_FLOW_COLUMN);
        lv_obj_set_style_bg_opa(container, LV_OPA_TRANSP, 0);
        lv_obj_set_size(container, LV_PCT(100), LV_SIZE_CONTENT);
        lv_obj_t *img = lv_img_custom_create(container, &icon_brightness);
        lv_obj_t *label = lv_label_custom_create(container, get_text(text_brightness), FONT_POP_WINDOW_TITLE,
                                                 lv_color_white(), LV_TEXT_ALIGN_CENTER);
        lv_obj_set_flex_align(container, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);

        comp_fine_tuning_slider_init(v_layout, ft_slider_brightness);

        // lv_obj_custom_trans_create(v_layout, LV_PCT(100), 200);
        setting_roller_init(v_layout, get_text(text_standby), get_text(text_standby_option), roller_setting_standby);
        setting_roller_init(v_layout, get_text(text_shutdown), get_text(text_shutdown_option), roller_setting_off);
    } else if (Setting.selected_item == setting_reset) {
        // 重置页
        lv_obj_set_style_pad_gap(v_layout, 10, 0);
        // 水平布局
        lv_obj_t *h_layout = lv_layout_custom_create(v_layout, LV_FLEX_FLOW_ROW);
        lv_obj_set_size(h_layout, LV_PCT(100), 74);
        lv_obj_set_style_radius(h_layout, RADIUS_DEFAULT, 0);
        lv_obj_set_style_pad_gap(h_layout, 0, 0);
        lv_obj_set_style_bg_color(h_layout, lv_color_hex(Bg_Color_Black), 0);
        lv_obj_set_style_bg_opa(h_layout, LV_OPA_COVER, 0);
        lv_obj_set_style_clip_corner(h_layout, true, LV_PART_MAIN | LV_STATE_DEFAULT);
        uint8_t pop_type = pop_reset;
        lv_obj_add_event_cb(h_layout, pop_window_event_cb, LV_EVENT_ALL, (void *) (uintptr_t) pop_type);

        lv_obj_t *res_layout = lv_layout_custom_create(h_layout, LV_FLEX_FLOW_ROW);
        lv_obj_set_style_radius(res_layout, RADIUS_DEFAULT, 0);
        lv_obj_set_size(res_layout, LV_PCT(50), LV_PCT(100));
        lv_obj_set_style_bg_opa(res_layout, LV_OPA_COVER, 0);
        lv_obj_set_style_bg_color(res_layout, lv_color_hex(Bg_Color_Gray), 0);
        lv_obj_t *res_label = lv_label_custom_create(res_layout, get_text(text_reset), FONT_SETTING_ITEM,
                                                     lv_color_white(), LV_TEXT_ALIGN_CENTER);
        lv_obj_set_flex_align(res_layout, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
        // 禁止点击，避免影响h_layout事件
        lv_obj_clear_flag(res_layout, LV_OBJ_FLAG_CLICKABLE);

        lv_obj_t *img_layout = lv_layout_custom_create(h_layout, LV_FLEX_FLOW_ROW);
        lv_obj_set_size(img_layout, LV_PCT(50), LV_PCT(100));
        lv_obj_set_style_bg_opa(img_layout, LV_OPA_TRANSP, 0);
        lv_obj_set_flex_align(img_layout, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
        // lv_obj_set_style_border_width(img_layout, 5, 0);
        lv_obj_t *img = lv_img_custom_create(img_layout, &icon_restore_factory);
        // 禁止点击，避免影响h_layout事件
        lv_obj_clear_flag(img_layout, LV_OBJ_FLAG_CLICKABLE);

        lv_obj_t *info_layout = lv_layout_custom_create(v_layout, LV_FLEX_FLOW_COLUMN);
        lv_obj_set_style_pad_gap(info_layout, 16 + 10, 0);
        lv_obj_set_style_pad_hor(info_layout, 30, 0);
        lv_obj_set_size(info_layout, LV_PCT(100), 198);
        lv_obj_set_style_radius(info_layout, RADIUS_DEFAULT, 0);
        lv_obj_set_style_bg_color(info_layout, lv_color_hex(Bg_Color_Black), 0);
        lv_obj_set_flex_align(info_layout, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START);

        char model_str[30];
#if ProductModel == QZ_N
        snprintf(model_str, sizeof(model_str), "%sQZ-N", get_text(text_model));
#elif ProductModel == QZ_F
        snprintf(model_str, sizeof(model_str), "%sQZ-F", get_text(text_model));
#endif
        lv_obj_t *model_label = lv_label_custom_create(info_layout, model_str, FONT_VER, lv_color_white(),
                                                       LV_TEXT_ALIGN_LEFT);
        char version_str[40];
        version_to_string(&Ver, version_str, sizeof(version_str));
        lv_obj_t *ver_label = lv_label_custom_create(info_layout, version_str, FONT_VER, lv_color_white(),
                                                     LV_TEXT_ALIGN_LEFT);
        lv_obj_set_style_text_opa(ver_label, MY_LABEL_OPA, 0);
        border_focused_obj_init(h_layout, &Pages.page[Page_Setting_Detail], Focused_Opr_Parent);
    }
}

