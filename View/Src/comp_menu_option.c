//
// Created by <PERSON><PERSON> on 2024/7/9.
//

#include "comp_menu_option.h"
#include "comp_status_bar.h"
#include "comp_border_focused.h"
#include "view_model.h"
#include "multi_language.h"
#include "page_setting.h"

void set_switcher_status(lv_obj_t *switcher, uint8_t bool_value) {
    if (bool_value) {
        // 打开 switch
        lv_obj_add_state(switcher, LV_STATE_CHECKED);
    } else {
        // 关闭 switch
        lv_obj_clear_state(switcher, LV_STATE_CHECKED);
    }
}

void control_center_value_set(OptionStruct *options, bool is_init) {
    switch (options->setting_type) {
        case setting_beep:
            if (Setting.values[setting_beep]) {
                lv_obj_set_style_bg_color(options->obj_bg, lv_color_hex(Bg_Color_Black), 0);
                lv_img_set_src(options->obj_img, &icon_vol_off);
            } else {
                lv_obj_set_style_bg_color(options->obj_bg, lv_color_hex(Main_Color), 0);
                lv_img_set_src(options->obj_img, &icon_vol_on);
            }
            break;
        case setting_lock:
            if (Setting.lock) {
                lv_obj_set_style_bg_color(options->obj_bg, lv_color_hex(Main_Color), 0);
                lv_img_set_src(options->obj_img, &icon_locked);
            } else {
                lv_obj_set_style_bg_color(options->obj_bg, lv_color_hex(Bg_Color_Black), 0);
                lv_img_set_src(options->obj_img, &icon_unlock);
            }
            break;
        case setting_lamp:
            if (Setting.lamp_main_switch) {
                lv_obj_set_style_bg_color(options->obj_bg, lv_color_hex(Main_Color), 0);
                // 从初始化触发的就修改滑动条状态，只有从VALUE_CHANGE触发的才修改
                if (!is_init) {
                    for (int i = 0; i < GroupItemCount; ++i) {
                        Param.group[i].stylish_lamp_mode = lamp_manual;
                        if (Pages.page[Page_Home].state == page_created) {
                            lv_event_send(Param.group[i].slider_home.slider_bg, LV_EVENT_VALUE_CHANGED, NULL);
                        } else if (Pages.page[Page_Group_Info].state == page_created) {
                            lv_event_send(Param.group[i].slider_option_lamp_level.slider_bg, LV_EVENT_VALUE_CHANGED,
                                          NULL);
                        }
                    }
                }
            } else {
                lv_obj_set_style_bg_color(options->obj_bg, lv_color_hex(Bg_Color_Black), 0);
                if (!is_init) {
                    for (int i = 0; i < GroupItemCount; ++i) {
                        Param.group[i].stylish_lamp_mode = lamp_off;
                        if (Pages.page[Page_Home].state == page_created) {
                            lv_event_send(Param.group[i].slider_home.slider_bg, LV_EVENT_VALUE_CHANGED, NULL);
                        } else if (Pages.page[Page_Group_Info].state == page_created) {
                            lv_event_send(Param.group[i].slider_option_lamp_level.slider_bg, LV_EVENT_VALUE_CHANGED,
                                          NULL);
                        }
                    }
                }
            }
            break;
        case setting_tcm:
            if (Setting.values[setting_tcm]) {
                lv_obj_set_style_bg_color(options->obj_bg, lv_color_hex(Main_Color), 0);
            } else {
                lv_obj_set_style_bg_color(options->obj_bg, lv_color_hex(Bg_Color_Black), 0);
            }
            if (!is_init) {
                for (int i = 0; i <= group_C; ++i) {
                    if (Pages.page[Page_Home].state == page_created) {
                        if (Param.group[i].mode == mode_M) {
                            lv_event_send(Param.group[i].slider_home.slider_bg, LV_EVENT_VALUE_CHANGED, NULL);
                        }
                    } else if (Pages.page[Page_Group_Info].state == page_created) {
                        if (Param.group[i].mode == mode_M) {
                            lv_event_send(Param.group[i].slider_option_flash_level.slider_bg, LV_EVENT_VALUE_CHANGED,
                                          NULL);
                        }
                    }
                }
            }
            break;
        default:
            break;
    }
}

static void lock_mask_event_cb(lv_event_t *e) {
    lv_event_code_t code = lv_event_get_code(e);
    lv_obj_t *tar_obj = lv_event_get_target(e);
    static bool mask_need_del;
    if (code == LV_EVENT_LONG_PRESSED) {
        // 先隐藏，松手再删除，避免直接触发下方的对象
        lv_obj_add_flag(tar_obj, LV_OBJ_FLAG_HIDDEN);
        mask_need_del = true;
    } else if (code == LV_EVENT_RELEASED) {
        if (mask_need_del) {
            lv_obj_del(tar_obj);
            Setting.lock = false;
            mask_need_del = false;
        }
    }
}

// 设置页各选项回调
static void option_event_cb(lv_event_t *e) {
    if (!Anim.is_finished_anim) {
        return;
    }
    lv_event_code_t code = lv_event_get_code(e);
    lv_obj_t *tar_obj = lv_event_get_target(e);
    OptionStruct *option_struct = lv_obj_get_user_data(tar_obj);
    static bool is_short_clicked;
    static uint16_t count_pressing;
    if (code == LV_EVENT_PRESSING) {
        lv_point_t vect;
        lv_indev_get_vect(lv_indev_get_act(), &vect);
        LV_LOG("vect.x: %d, vect.y: %d\n", vect.x, vect.y);
        count_pressing++;
        LV_LOG("count_pressing: %d, ", count_pressing);
        if (abs(vect.x) < 2 && abs(vect.y) < 2 && count_pressing < Short_Click_Sensitivity) {
            LV_LOG("true.\n");
            is_short_clicked = true;
        } else {
            LV_LOG("false.\n");
            is_short_clicked = false;
        }
        // LV_LOG("count_pressing: %d\n", count_pressing);
        // if (count_pressing > Short_Click_Sensitivity) {
        //     is_short_clicked = false;
        // } else {
        //     is_short_clicked = true;
        // }
    } else if (code == LV_EVENT_RELEASED) {
        count_pressing = 0;
    } else if (code == LV_EVENT_SHORT_CLICKED || code == LV_EVENT_KEY) {
        if (code == LV_EVENT_KEY) {
            char c = *((char *) lv_event_get_param(e));
            if (c == LV_KEY_ENTER) {
                is_short_clicked = true;
            } else if (c == LV_KEY_HOME || c == LV_KEY_END) {
                page_click_anim(pos_bottom, &Pages.page[Page_Home], anim_overlay);
            } else {
                return;
            }
        }
        if (is_short_clicked && Anim.is_finished_anim) {
            // if (Anim.is_finished_anim) {
            // 开关VALUE_CHANGED，跳转在点按处理
            if (option_struct->option_type == opt_ctrl_center || option_struct->had_switch) {
                switch (option_struct->setting_type) {
                    case setting_beep:
                        Setting.values[setting_beep] = !Setting.values[setting_beep];
                        set_beep_lamp_level();
                        break;
                    case setting_lock:
                        Setting.lock = !Setting.lock;
                        lv_obj_t *mask = lv_layout_custom_create(lv_layer_top(), LV_FLEX_FLOW_COLUMN);
                        lv_obj_add_style(mask, &PageManager.default_style, 0);
                        lv_obj_set_style_bg_color(mask, lv_color_black(), 0);
                        lv_obj_set_style_bg_opa(mask, LV_OPA_80, 0);
                        lv_obj_set_size(mask, LV_PCT(100), LV_PCT(100));
                        lv_obj_set_flex_align(mask, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER,
                                              LV_FLEX_ALIGN_CENTER);
                        lv_obj_add_event_cb(mask, lock_mask_event_cb, LV_EVENT_ALL, NULL);

                        lv_obj_t *v_layout = lv_layout_custom_create(mask, LV_FLEX_FLOW_COLUMN);
                        lv_obj_set_size(v_layout, LV_PCT(100), LV_SIZE_CONTENT);
                        lv_obj_set_style_pad_hor(v_layout, 68, 0);
                        lv_obj_set_style_bg_opa(v_layout, LV_OPA_TRANSP, 0);
                        lv_obj_set_flex_align(v_layout, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER,
                                              LV_FLEX_ALIGN_CENTER);
                        lv_obj_clear_flag(v_layout, LV_OBJ_FLAG_CLICKABLE);
                        lv_obj_t *img = lv_img_custom_create(v_layout, &icon_locked_100);
                        lv_obj_t *label = lv_label_custom_create(v_layout,
                                                                 get_text(text_unlock_info),
                                                                 FONT_POP_WINDOW_INFO, lv_color_white(),
                                                                 LV_TEXT_ALIGN_CENTER);
                        lv_obj_set_width(label, Screen_Width - 68 * 2);
                        lv_label_set_long_mode(label, LV_LABEL_LONG_WRAP);
                        Pages.lock_mask = mask;
                        break;
                    case setting_lamp:
                        Setting.lamp_main_switch = !Setting.lamp_main_switch;
                        break;
                    case setting_tcm:
                        Setting.values[setting_tcm] = !Setting.values[setting_tcm];
                        if (Setting.values[setting_shoot]) {
                            Setting.values[setting_shoot] = false;
                            update_status_bar();
                            lv_event_send(option_shoot.obj_bg, LV_EVENT_VALUE_CHANGED, NULL);
                        }
                        // 修改被覆盖的页面
                        for (int i = 0; i <= group_C; ++i) {
                            if (Pages.page[Page_Home].state == page_created) {
                                if (Param.group[i].mode == mode_M) {
                                    lv_event_send(Param.group[i].slider_home.slider_bg, LV_EVENT_VALUE_CHANGED, NULL);
                                }
                            } else if (Pages.page[Page_Group_Info].state == page_created) {
                                if (Param.group[i].mode == mode_M) {
                                    lv_event_send(Param.group[i].slider_option_flash_level.slider_bg,
                                                  LV_EVENT_VALUE_CHANGED, NULL);
                                }
                            }
                        }
                        break;
                    case setting_shoot:
                        Setting.values[setting_shoot] = !Setting.values[setting_shoot];
                        if (Setting.values[setting_tcm]) {
                            Setting.values[setting_tcm] = false;
                            lv_event_send(option_tcm.obj_bg, LV_EVENT_VALUE_CHANGED, NULL);
                            // 修改被覆盖的页面
                            for (int i = 0; i <= group_C; ++i) {
                                if (Pages.page[Page_Home].state == page_created) {
                                    if (Param.group[i].mode == mode_M) {
                                        lv_event_send(Param.group[i].slider_home.slider_bg, LV_EVENT_VALUE_CHANGED,
                                                      NULL);
                                    }
                                } else if (Pages.page[Page_Group_Info].state == page_created) {
                                    if (Param.group[i].mode == mode_M) {
                                        lv_event_send(Param.group[i].slider_option_flash_level.slider_bg,
                                                      LV_EVENT_VALUE_CHANGED, NULL);
                                    }
                                }
                            }
                        }
                        update_status_bar();
                        // 修改被覆盖的页面
                        for (int i = 0; i < GroupItemCount; ++i) {
                            if (Pages.page[Page_Home].state == page_created) {
                                if (Param.group[i].mode == mode_TTL) {
                                    Param.group[i].mode = mode_M;
                                    lv_event_send(Param.group[i].slider_home.slider_bg, LV_EVENT_VALUE_CHANGED, NULL);
                                }
                            } else if (Pages.page[Page_Group_Info].state == page_created) {
                                if (Param.group[i].mode == mode_TTL) {
                                    Param.group[i].mode = mode_M;
                                    lv_event_send(Param.group[i].mode_layout,
                                                  LV_EVENT_VALUE_CHANGED, NULL);
                                    lv_event_send(Param.group[i].slider_option_flash_level.slider_bg,
                                                  LV_EVENT_VALUE_CHANGED, NULL);
                                }
                            }
                        }
                        break;
#if MODULE_BT
                        case setting_bluetooth:
                            Setting.values[setting_bluetooth] = !Setting.values[setting_bluetooth];
                            break;
#endif
                    default:
                        break;
                }
                lv_event_send(tar_obj, LV_EVENT_VALUE_CHANGED, NULL);
            } else {
                Setting.selected_item = option_struct->setting_type;
                // 创建页面
                pm_creat_page(PageManager.main_page->pos[pos_right].page, PageManager.main_page, LV_ALIGN_OUT_RIGHT_MID,
                              lv_color_black());
                page_click_anim(pos_right, &Pages.page[Page_Setting_Detail], anim_overlay);
                Pages.page[Page_Setting_Detail].is_overlay_active = true;
                Pages.page[Page_Setting_Detail].overlay_page = &Pages.page[Page_Setting];
                // lv_obj_clear_flag(tar_obj, LV_OBJ_FLAG_CLICKABLE);
            }
        }
    } else if (code == LV_EVENT_VALUE_CHANGED) {
        if (option_struct->option_type == opt_ctrl_center) {
            // 更新下拉菜单状态
            control_center_value_set(option_struct, false);
            if (option_struct->setting_type == setting_lamp) {
                set_beep_lamp_level();
            }
            if (option_struct->setting_type == setting_lock) {
                page_click_anim(pos_bottom, PageManager.main_page->overlay_page, anim_overlay);
            }
        } else {
            lv_obj_t *switcher = lv_obj_get_child(tar_obj, 1);
            // 设置switcher状态
            set_switcher_status(switcher, !lv_obj_has_state(switcher, LV_STATE_CHECKED));
            // 手动触发VALUE_CHANGED事件来模拟switch被点击，以实现保留动画效果
            lv_event_send(switcher, LV_EVENT_VALUE_CHANGED, NULL);

            // 强制重绘以确保动画效果
            // lv_obj_invalidate(switcher);
        }
    }
    // else if (code == LV_EVENT_DRAW_MAIN_END){
    //     lv_event_send(tar_obj, LV_EVENT_VALUE_CHANGED, NULL);
    // }
}

void comp_menu_option_init(lv_obj_t *parent, OptionStruct *options, PageTypeHandle *page) {
    LV_LOG("menu_option_init\n");
    lv_obj_t *v_layout = lv_layout_custom_create(parent, LV_FLEX_FLOW_COLUMN);
    lv_obj_set_size(v_layout, 162, 116);
    lv_obj_set_style_pad_gap(v_layout, 0, LV_PART_MAIN);
    lv_obj_set_style_bg_color(v_layout, lv_color_hex(Bg_Color_Black), 0);
    lv_obj_set_style_radius(v_layout, RADIUS_DEFAULT, 0);
    lv_obj_set_flex_align(v_layout, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_add_event_cb(v_layout, option_event_cb, LV_EVENT_ALL, NULL);
    options->obj_bg = v_layout;
    lv_obj_t *label = NULL, *img = NULL;
    if (options->img_src == NULL) {
        label = lv_label_custom_create(v_layout, options->str, FONT_MENU_OPTION, lv_color_white(),
                                       LV_TEXT_ALIGN_CENTER);
    } else {
        img = lv_img_custom_create(v_layout, options->img_src);
        options->obj_img = img;
    }
    //lv_obj_center(img);
    if (options->had_switch) {
        lv_obj_t *switcher = lv_switch_create(v_layout);
        lv_obj_set_style_bg_color(switcher, lv_color_hex(Bg_Color_Gray), 0);
        lv_obj_set_width(switcher, 66);
        lv_obj_set_style_bg_color(switcher, lv_color_hex(Main_Color), LV_PART_INDICATOR | LV_STATE_CHECKED);
        // 设置初始化状态
        set_switcher_status(switcher, Setting.values[options->setting_type]);
        // 去除点击状态，避免点击时只改变switcher状态，并未触发option_event_cb改值
        lv_obj_clear_flag(switcher, LV_OBJ_FLAG_CLICKABLE);
    }
    if (options->option_type == opt_ctrl_center) {
        control_center_value_set(options, true);
    }
    border_focused_obj_init(v_layout, page, Focused_Scroll_Parent | Focused_Opr_Parent);

    lv_obj_set_user_data(v_layout, options);
}
