//
// Created by <PERSON><PERSON> on 2024/7/26.
//

#include "page_multi.h"
#include "comp_page_indicator.h"
#include "comp_grp_switch.h"
#include "comp_status_bar.h"
#include "comp_border_focused.h"
#include "view_model.h"
#include "utils.h"

// Roller滑出界后会死机
// 问题出现在lv_refr.c内的refr_area内的for循环为死循环
// 进一步查实原因为area_p->y1(330) > area_p->y2(329)
// lv_area_get_height为0,即max_row为0
// for循环内的循环变量增值(row += max_row)为0导致了死循环

lv_obj_t *handle_multi_right;
lv_obj_t *handle_multi_top;

uint8_t roller_no_anim_param = 1;

static lv_point_t last_point;

static uint8_t pressing_count[5] = {0, 0, 0, 0, 0}, is_changed_value = 0;

uint8_t roller_height = 180;

const uint8_t Multi_max_times_table[8][13] = {
        {100, 100, 100, 100, 100, 90, 80, 70, 70, 60, 50, 40, 40}, // 1/512
        {100, 100, 100, 100, 100, 90, 80, 70, 70, 60, 50, 40, 40}, // 1/256
        {100, 100, 100, 100, 100, 90, 80, 70, 70, 60, 50, 40, 40}, // 1/128
        {90,  90,  90,  80,  80,  70, 60, 50, 40, 40, 35, 30, 20}, // 1/64
        {60,  60,  60,  50,  50,  40, 30, 20, 20, 20, 18, 16, 12}, // 1/32
        {30,  30,  30,  20,  20,  20, 10, 10, 8,  8,  8,  8,  8}, // 1/16
        {14,  14,  12,  10,  8,   6,  5,  4,  4,  4,  4,  4,  4}, // 1/8
        {8,   6,   4,   3,   3,   2,  2,  2,  2,  2,  2,  2,  2}, // 1/4
};

// 获取最大次数的函数
uint8_t get_max_multi_flash_times(uint8_t flash_level_multi, uint8_t freq) {
    int freq_index;
    if (freq >= 60) {
        freq_index = 12;
    } else if (freq >= 20) {
        freq_index = 11;
    } else if (freq >= 15) {
        freq_index = 10;
    } else if (freq >= 12) {
        freq_index = 9;
    } else if (freq >= 11) {
        freq_index = 8;
    } else if (freq >= 10) {
        freq_index = 7;
    } else if (freq >= 8) {
        freq_index = 6;
    } else if (freq >= 6) {
        freq_index = 5;
    } else {
        freq_index = freq - 1;
    }

    // 获取频闪能级范围
    uint8_t multi_level_range = get_multi_level_range();
    if (flash_level_multi > multi_level_range) {
        flash_level_multi = multi_level_range;
    }

    // 根据multi_level_range调整flash_level_multi,避免错位
    // multi_level_range为7则不变，6则把能级+1作为下标，5则把能级+2作为下标
    // if (multi_level_range >= max_level[0] / 10 - 2 && multi_level_range <= max_level[MAX_FLASH_LEVEL_INDEX] / 10 - 2) {
    //     flash_level_multi += (max_level[MAX_FLASH_LEVEL_INDEX] / 10 - 2 - multi_level_range);
    // }
    if (multi_level_range >= 5 && multi_level_range <= 7) {
        flash_level_multi += (7 - multi_level_range);
    }

    return Multi_max_times_table[flash_level_multi][freq_index];
}

static void fine_tuning_slider_opr_pgr(FineTuningSliderEnum slider_type, OprValueEnum opr_type) {
    int8_t opr_value = 0;
    if (opr_type == opr_value_add) {
        opr_value = 1;
    } else {
        opr_value = -1;
    }
    if (slider_type == ft_slider_multi) {
        Param.multi.flash_level = level_adjust_handle(opr_value, Param.multi.flash_level, 0, get_multi_level_range(),
                                                      1);
        lv_event_send(Param.multi.slider, LV_EVENT_VALUE_CHANGED, NULL);
    } else if (slider_type == ft_slider_brightness) {
        Param.brightness.level = level_adjust_handle(opr_value, Param.brightness.level, 1, MAX_BRIGHTNESS_LEVEL, 1);
        lv_event_send(Param.brightness.slider, LV_EVENT_VALUE_CHANGED, NULL);
    }
}

// 频闪页滑动条事件处理
void fine_tuning_slider_event_handler(lv_event_t *e) {
    lv_obj_t *tar_obj = lv_event_get_target(e);
    lv_obj_t *current_tar_obj = lv_event_get_current_target(e);
    lv_obj_t *slider;
    lv_obj_t *slider_bg;
    static bool last_point_has_data = false;
    if (tar_obj == current_tar_obj) {
        slider_bg = tar_obj;
        slider = lv_obj_get_child(tar_obj, 0);
    } else {
        slider_bg = lv_obj_get_parent(tar_obj);
        slider = lv_obj_get_child(slider_bg, 0);
    }
    lv_event_code_t code = lv_event_get_code(e);
    lv_coord_t slider_width = lv_obj_get_width(slider_bg);
    FineTuningSliderEnum slider_type = (FineTuningSliderEnum) (uintptr_t) lv_event_get_user_data(e);

    if (code == LV_EVENT_PRESSING) {
        // LV_LOG("slider_pressing\n");
        // 按住
        // 检测滑动
        lv_point_t current_point;
        lv_indev_t *indev = lv_indev_get_act();
        if (indev) {
            lv_indev_get_point(indev, &current_point);
        }
        if (!last_point_has_data) {
            last_point = current_point;
            last_point_has_data = true;
            return;
        }
        lv_coord_t offset_x = last_point.x - current_point.x;
        last_point = current_point;
        LV_LOG("fine_tuning_slider offset_x %d\n", offset_x);

        uint8_t count_num, has_data_index;
        uint8_t steps[5] = {0, 1, 2, 2, 2};
        uint16_t thresholds[5] = {0, 20, 60, 999, 999};
        if (slider_type == ft_slider_multi) {
            count_num = 5 + 1;
            uint8_t multi_steps[] = {0, 1, 3, 5, 5};
            uint16_t multi_thresholds[] = {1, 12, 45, 999, 999};
            memcpy(steps, multi_steps, sizeof(steps));
            memcpy(thresholds, multi_thresholds, sizeof(thresholds));
        } else if (slider_type == ft_slider_brightness) {
            count_num = 6 + 1;
            uint8_t brightness_steps[] = {0, 1, 2, 3, 30};
            uint16_t brightness_thresholds[] = {1, 10, 30, 40, 999};
            memcpy(steps, brightness_steps, sizeof(steps));
            memcpy(thresholds, brightness_thresholds, sizeof(thresholds));
        }
        // 然后检查 offset_x 并更新 pressing_count
        uint8_t step = 0;
        for (int i = 5 - 1; i >= 0; i--) {
            if (pressing_count[i] > 0) {
                // LV_LOG("pressing_count[%d] = %d\n", i, pressing_count[i]);
                pressing_count[i]--;
                is_changed_value = (pressing_count[i] == 0 && i > 0) ? 1 : 0;
                has_data_index = i;
                break;
            }
        }
        for (int i = 0; i < 5; ++i) {
            if (LV_ABS(offset_x) <= thresholds[i]) {
                step = steps[i];
                if (!pressing_count[i]) {
                    // 清零所有较小索引的 pressing_count
                    for (int j = 0; j <= i; j++) {
                        pressing_count[j] = 0;
                    }
                    pressing_count[i] = count_num;
                    if (has_data_index < i) {
                        is_changed_value = 1;
                    } else {
                        // LV_LOG("has_data_index: %d\n", has_data_index);
                    }
                    // BUG导致的程序正常
                    // 此处本应放在if外，否则会导致i=0时仍触发is_changed_value
                    // 但只有如此才可让lv_event_send(Param.multi.times_roller, LV_EVENT_VALUE_CHANGED, NULL);触发两次
                    // 以实现次数的roller先带动画滚回最大限制再修改roller的字符串表
                    // 直接发送两次roller的lv_event_send(slider, LV_EVENT_VALUE_CHANGED, NULL);无法实现
                    // 疑似roller动画未完成就被调用了第二次LV_EVENT_VALUE_CHANGED导致的

                }
                if (i == 0) {
                    is_changed_value = 0;
                }
                break;
            }
        }

        if (is_changed_value) {
            if (slider_type == ft_slider_multi) {
                Param.multi.flash_level = level_adjust_handle(-offset_x, Param.multi.flash_level,
                                                              0, get_multi_level_range(), step);
            } else {
                Param.brightness.level = level_adjust_handle(-offset_x, Param.brightness.level,
                                                             1, MAX_BRIGHTNESS_LEVEL, step);
            }

            lv_event_send(slider, LV_EVENT_VALUE_CHANGED, NULL);
        }
    } else if (code == LV_EVENT_RELEASED) {
        LV_LOG("slider_RELEASED\n");
        // LV_LOG("------re\n");
        last_point_has_data = false;
        memset(pressing_count, 0, sizeof(pressing_count));
        last_point.x = 0;
        last_point.y = 0;
    } else if (code == LV_EVENT_VALUE_CHANGED) {
        char level_str[6];
        lv_coord_t slider_level_width;
        if (slider_type == ft_slider_multi) {
            LV_LOG("Param.multi.flash_level: %d\n", Param.multi.flash_level);
            if (Setting.roller_values[roller_min_power] / MAX_FLASH_LEVEL_INDEX == 0) {
                strncpy(level_str, get_str_flash_level_Multi(Param.multi.flash_level), sizeof(level_str));
            } else {
                strncpy(level_str, get_decimals_str_flash_level_M(Param.multi.flash_level * 10), sizeof(level_str));
            }
            // 确保以 '\0' 结尾
            level_str[sizeof(level_str) - 1] = '\0';
            LV_LOG("multi_level_str: %s\n", level_str);
            lv_label_set_text(Param.multi.label_flash_level, level_str);
            slider_level_width = cal_slider_width_by_level(slider_width, Param.multi.flash_level,
                                                           get_multi_level_range());
            // char *is_send_event = "true";
            lv_event_send(Param.multi.times_roller, LV_EVENT_VALUE_CHANGED, (void *) (uintptr_t) roller_no_anim_param);
            set_multi_param();
        } else {
            sprintf(level_str, "%d%%", Param.brightness.level * (100 / MAX_BRIGHTNESS_LEVEL));
            // 确保以 '\0' 结尾
            level_str[sizeof(level_str) - 1] = '\0';
            lv_label_set_text(Param.brightness.label_level, level_str);
            slider_level_width = cal_slider_width_by_level(slider_width, Param.brightness.level,
                                                           MAX_BRIGHTNESS_LEVEL);
            set_screen_brightness();
        }
        lv_obj_set_width(slider, slider_level_width);
    } else if (code == LV_EVENT_KEY) {
        char c = *((char *) lv_event_get_param(e));
        if (c == LV_KEY_LEFT) {
            fine_tuning_slider_opr_pgr(slider_type, opr_value_add);
        } else if (c == LV_KEY_RIGHT) {
            fine_tuning_slider_opr_pgr(slider_type, opr_value_sub);
        } else if (c == LV_KEY_ESC) {
            if (!lv_group_get_editing(lv_group_get_default()) && slider_type == ft_slider_multi) {
                // 创建页面
                pm_creat_page(PageManager.main_page->pos[pos_right].page, PageManager.main_page, LV_ALIGN_OUT_RIGHT_MID,
                              lv_color_black());
                page_click_anim(pos_right, &Pages.page[Page_Home], anim_slide);
            }
        } else if (c == LV_KEY_END) {
            if (slider_type == ft_slider_multi) {
                // 创建页面
                pm_click_to_page_control_center();
            } else if (slider_type == ft_slider_brightness) {
                page_click_anim(pos_left, &Pages.page[Page_Setting], anim_overlay);
            }
        } else if (c == LV_KEY_HOME) {
            if (slider_type == ft_slider_brightness) {
                page_click_anim(pos_left, &Pages.page[Page_Setting], anim_overlay);
            }
        }
    }
}

void slider_add_event_cb(lv_event_t *e) {
    lv_event_code_t code = lv_event_get_code(e);
    FineTuningSliderEnum slider_type = (FineTuningSliderEnum) (uintptr_t) lv_event_get_user_data(e);
    static bool is_short_clicked;
    static uint16_t count_pressing;
    if (code == LV_EVENT_PRESSING) {
        lv_point_t vect;
        lv_indev_get_vect(lv_indev_get_act(), &vect);
        LV_LOG("vect.x: %d, vect.y: %d\n", vect.x, vect.y);
        count_pressing++;
        LV_LOG("count_pressing: %d, ", count_pressing);
        if (abs(vect.x) < 2 && abs(vect.y) < 2 && count_pressing < Short_Click_Sensitivity) {
            LV_LOG("is_short_clicked\n");
            is_short_clicked = true;
        } else {
            LV_LOG("not_short_clicked\n");
            is_short_clicked = false;
        }
    } else if (code == LV_EVENT_RELEASED) {
        count_pressing = 0;
    } else if (code == LV_EVENT_SHORT_CLICKED) {
        if (is_short_clicked) {
            LV_LOG("slider_add_event_cb\n");
            fine_tuning_slider_opr_pgr(slider_type, opr_value_add);
        }
    }
}

void slider_reduce_event_cb(lv_event_t *e) {
    lv_event_code_t code = lv_event_get_code(e);
    FineTuningSliderEnum slider_type = (FineTuningSliderEnum) (uintptr_t) lv_event_get_user_data(e);
    static bool is_short_clicked;
    static uint16_t count_pressing;
    if (code == LV_EVENT_PRESSING) {
        lv_point_t vect;
        lv_indev_get_vect(lv_indev_get_act(), &vect);
        LV_LOG("vect.x: %d, vect.y: %d\n", vect.x, vect.y);
        count_pressing++;
        LV_LOG("count_pressing: %d, ", count_pressing);
        if (abs(vect.x) < 2 && abs(vect.y) < 2 && count_pressing < Short_Click_Sensitivity) {
            LV_LOG("is_short_clicked\n");
            is_short_clicked = true;
        } else {
            LV_LOG("not_short_clicked\n");
            is_short_clicked = false;
        }
    } else if (code == LV_EVENT_RELEASED) {
        count_pressing = 0;
    } else if (code == LV_EVENT_SHORT_CLICKED) {
        if (is_short_clicked) {
            LV_LOG("slider_reduce_event_cb\n");
            fine_tuning_slider_opr_pgr(slider_type, opr_value_sub);
        }
    }

}

// 带加减号微调的滑动条
void comp_fine_tuning_slider_init(lv_obj_t *parent, uint8_t slider_type) {
    const uint8_t radius_value = 32;
    uint8_t slider_h;
    const lv_font_t *level_font;
    char level_str[6];
    if (slider_type == ft_slider_multi) {
        slider_h = 90;
        level_font = FONT_MULTI_LEVEL;
        if (Setting.roller_values[roller_min_power] / MAX_FLASH_LEVEL_INDEX == 0) {
            strncpy(level_str, get_str_flash_level_Multi(Param.multi.flash_level), sizeof(level_str));
        } else {
            strncpy(level_str, get_decimals_str_flash_level_M(Param.multi.flash_level * 10), sizeof(level_str));
        }
    } else {
        slider_h = 76;
        level_font = FONT_BRIGHTNESS_LEVEL;
        sprintf(level_str, "%d%%", Param.brightness.level * (100 / MAX_BRIGHTNESS_LEVEL));
    }
    // 确保以 '\0' 结尾
    level_str[sizeof(level_str) - 1] = '\0';

    // 滑动条背景
    lv_obj_t *slider_bg = lv_obj_create(parent);
    lv_obj_set_size(slider_bg, LV_PCT(100), slider_h);
    lv_obj_add_style(slider_bg, &PageManager.default_style, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_radius(slider_bg, radius_value, 0);
    lv_obj_set_style_bg_color(slider_bg, lv_color_hex(Bg_Color_Black), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(slider_bg, LV_OPA_COVER, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_clip_corner(slider_bg, true, 0);
    lv_obj_set_style_pad_gap(slider_bg, 0, 0);
    lv_obj_add_event_cb(slider_bg, fine_tuning_slider_event_handler, LV_EVENT_ALL, (void *) (uintptr_t) slider_type);
    // (uintptr_t)将整数类型强制转换为 void*
    // 以便将单个变量作为user_data
    // lv_obj_set_user_data(slider_bg, (void *) (uintptr_t) slider_type);

    // 滑动条
    lv_obj_t *slider = lv_obj_create(slider_bg);
    lv_obj_update_layout(slider_bg);
    lv_coord_t slider_width = lv_obj_get_width(slider_bg);
    // 当前滑条长度
    lv_coord_t slider_level_width;
    if (slider_type == ft_slider_multi) {
        slider_level_width = cal_slider_width_by_level(slider_width, Param.multi.flash_level,
                                                       get_multi_level_range());
    } else {
        slider_level_width = cal_slider_width_by_level(slider_width, Param.brightness.level,
                                                       MAX_BRIGHTNESS_LEVEL);
    }

    lv_obj_set_size(slider, slider_level_width, LV_PCT(100));
    lv_obj_add_style(slider, &PageManager.default_style, LV_PART_MAIN | LV_STATE_DEFAULT);
    // lv_obj_set_style_bg_opa(slider, LV_OPA_TRANSP, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(slider, lv_color_hex(Main_Color), 0);
    lv_obj_set_align(slider, LV_ALIGN_LEFT_MID);
    lv_obj_add_flag(slider, LV_OBJ_FLAG_EVENT_BUBBLE);

    // 加减号、文字
    lv_obj_t *h_layout = lv_layout_custom_create(slider_bg, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(h_layout, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_set_size(h_layout, LV_PCT(100), LV_PCT(100));
    lv_obj_set_style_bg_opa(h_layout, LV_OPA_TRANSP, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_hor(h_layout, 16, 0);
    lv_obj_set_style_pad_gap(h_layout, 0, 0);
    lv_obj_move_foreground(h_layout);
    lv_obj_clear_flag(h_layout, LV_OBJ_FLAG_CLICKABLE);
    lv_obj_update_layout(h_layout);

    lv_obj_t *reduce_ico = lv_img_custom_create(h_layout, &icon_reduce_40);
    lv_obj_add_flag(reduce_ico, LV_OBJ_FLAG_CLICKABLE);
    lv_obj_add_event_cb(reduce_ico, slider_reduce_event_cb, LV_EVENT_ALL, (void *) (uintptr_t) slider_type);
    lv_obj_set_style_pad_all(reduce_ico, 10, 0);

    lv_obj_t *level_label = lv_label_custom_create(h_layout, level_str, level_font, lv_color_white(),
                                                   LV_TEXT_ALIGN_CENTER);
    lv_coord_t font_height = lv_font_get_line_height(level_font);
    // 图片大小40，水平间距16，图片内边距10
    lv_obj_set_size(level_label, lv_obj_get_width(h_layout) - (40 + 10 * 2) * 2 - 16 * 2, font_height);

    lv_obj_t *add_ico = lv_img_custom_create(h_layout, &icon_add_40);
    lv_obj_add_flag(add_ico, LV_OBJ_FLAG_CLICKABLE);
    lv_obj_add_event_cb(add_ico, slider_add_event_cb, LV_EVENT_ALL, (void *) (uintptr_t) slider_type);
    lv_obj_set_style_pad_all(add_ico, 10, 0);

    // if (slider_type == ft_slider_multi) {
    //     lv_obj_set_ext_click_area(reduce_ico, 40 + 5);
    //     lv_obj_set_ext_click_area(add_ico, 40 + 5);
    // }

    if (slider_type == ft_slider_multi) {
        Param.multi.slider = slider_bg;
        Param.multi.label_flash_level = level_label;
        border_focused_obj_init(slider_bg, &Pages.page[Page_Multi], Focused_Editing_Mode | Focused_Opr_Parent);
    } else {
        Param.brightness.slider = slider_bg;
        Param.brightness.label_level = level_label;
        border_focused_obj_init(slider_bg, &Pages.page[Page_Setting_Detail], Focused_Editing_Mode | Focused_Opr_Parent);
    }
}

static void animation_complete_cb(lv_timer_t *timer) {
    char *times_option_new_str = timer->user_data;
    lv_roller_set_options(Param.multi.times_roller, times_option_new_str, LV_ROLLER_MODE_NORMAL);
    lv_roller_set_selected(Param.multi.times_roller, Param.multi.flash_times - 1, LV_ANIM_OFF);
    // 删除定时器
    lv_timer_del(timer);
    free_and_clear(times_option_new_str);
}

void roller_event_handler(lv_event_t *e) {
    lv_obj_t *tar_obj = lv_event_get_target(e);
    lv_event_code_t code = lv_event_get_code(e);
    RollerStructType *roller_struct = lv_obj_get_user_data(tar_obj);
    if (code == LV_EVENT_VALUE_CHANGED) {
        if (roller_struct->roller_type == roller_multi_times) {
            uint8_t roller_selected_now = lv_roller_get_selected(tar_obj);
            Param.multi.flash_times = multi_times_arr[roller_selected_now];
            uint8_t max_times = get_max_multi_flash_times(Param.multi.flash_level, Param.multi.flash_freq);
            uint8_t new_arr_size = binary_search_index(multi_times_arr, times_arr_size, max_times) + 1;
            LV_LOG("max_times: %d\tindex: %d\n", max_times, new_arr_size);
            uint8_t new_arr[new_arr_size];
            memcpy(new_arr, multi_times_arr, new_arr_size * sizeof(uint8_t));
            char *times_option_new_str = convert_times_arr_to_str(new_arr, new_arr_size * sizeof(uint8_t));
            if (Param.multi.flash_times > max_times) {
                Param.multi.flash_times = max_times;
                lv_roller_set_selected(tar_obj, new_arr_size - 1, LV_ANIM_ON);
                uint32_t anim_time = lv_obj_get_style_anim_time(tar_obj, LV_PART_MAIN);
                // 创建一个定时器，延迟执行回调函数
                lv_timer_t *timer = lv_timer_create(animation_complete_cb, anim_time, times_option_new_str);
                lv_timer_set_repeat_count(timer, 1);
            } else {
                // e->param = NULL;
                if (lv_event_get_param(e) != NULL) {
                    uint8_t get_param_bool = (uintptr_t) lv_event_get_param(e);
                    LV_LOG("---%d---\n", get_param_bool);
                    // 从滑条来的LV_EVENT_VALUE_CHANGED才执行，避免直接点击没有动画
                    if (get_param_bool == roller_no_anim_param) {
                        lv_roller_set_options(Param.multi.times_roller, times_option_new_str, LV_ROLLER_MODE_NORMAL);
                        lv_roller_set_selected(Param.multi.times_roller, roller_selected_now, LV_ANIM_OFF);
                        free_and_clear(times_option_new_str);
                    }
                }
            }
            LV_LOG("Param.multi.flash_times: %d\n", Param.multi.flash_times);
        } else if (roller_struct->roller_type == roller_multi_hz) {
            uint8_t roller_selected_now = lv_roller_get_selected(tar_obj);
            Param.multi.flash_freq = multi_hz_arr[roller_selected_now];
            // char *is_send_event = "true";
            lv_event_send(Param.multi.times_roller, LV_EVENT_VALUE_CHANGED, (void *) (uintptr_t) roller_no_anim_param);
            LV_LOG("Param.multi.flash_freq: %d\n", Param.multi.flash_freq);
        }
        set_multi_param();
    } else if (code == LV_EVENT_KEY) {
        char c = *((char *) lv_event_get_param(e));
        if (c == LV_KEY_ESC) {
            if (!lv_group_get_editing(lv_group_get_default())) {
                // 创建页面
                pm_creat_page(PageManager.main_page->pos[pos_right].page, PageManager.main_page, LV_ALIGN_OUT_RIGHT_MID,
                              lv_color_black());
                page_click_anim(pos_right, &Pages.page[Page_Home], anim_slide);
            }
        } else if (c == LV_KEY_END) {
            pm_click_to_page_control_center();
        } else if (c == LV_KEY_LEFT || c == LV_KEY_RIGHT) {
            if (roller_struct->roller_type == roller_multi_hz) {
                lv_event_send(Param.multi.times_roller, LV_EVENT_VALUE_CHANGED,
                              (void *) (uintptr_t) roller_no_anim_param);
                lv_event_send(Param.multi.hz_roller, LV_EVENT_VALUE_CHANGED, NULL);
            } else if (roller_struct->roller_type == roller_multi_times) {
                // TODO 无动画待修复
                lv_event_send(Param.multi.times_roller, LV_EVENT_VALUE_CHANGED, NULL);
            }
            // char *is_send_event = "true";
            // lv_event_send(tar_obj, LV_EVENT_VALUE_CHANGED, is_send_event);
        }
    }
}

void comp_roller_init(lv_obj_t *parent, char *option_str, RollerMultiTypeEnumType roller_type) {
    lv_obj_t *roller_container = lv_layout_custom_create(parent, LV_FLEX_FLOW_ROW);
    lv_obj_set_style_bg_color(roller_container, lv_color_hex(Bg_Color_Black), 0);
    lv_obj_set_size(roller_container, 160, roller_height);
    lv_obj_set_style_radius(roller_container, RADIUS_DEFAULT, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(roller_container, 4, 0);
    lv_obj_set_style_pad_gap(roller_container, 9, 0);
    lv_obj_clear_flag(roller_container, LV_OBJ_FLAG_SCROLLABLE);
    lv_obj_set_flex_align(roller_container, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);

    lv_obj_custom_trans_create(roller_container, 35 - 9, 10);

    lv_obj_t *roller = lv_roller_create(roller_container);
    // lv_obj_remove_style_all(roller);
    // 禁用控件，防止获取焦点,触控也会失效
//    lv_obj_add_state(roller, LV_STATE_DISABLED);
    // lv_obj_set_style_pad_left(roller, 35, 0);
    lv_obj_set_style_text_line_space(roller, 25, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(roller, 3, LV_PART_MAIN | LV_STATE_CHECKED);
    lv_roller_set_options(roller, option_str, LV_ROLLER_MODE_NORMAL);
    lv_obj_set_width(roller, 68);
    lv_obj_set_ext_click_area(roller, 40);

    lv_obj_set_style_radius(roller, RADIUS_DEFAULT, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(roller, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(roller, LV_OPA_TRANSP, LV_PART_MAIN | LV_STATE_DEFAULT);
    // lv_obj_custom_set_bg_color(roller, Main_Color);
    lv_obj_set_style_text_color(roller, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(roller, LV_OPA_40, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(roller, FONT_MULTI_ROLLER_UNSELECTED, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(roller, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN | LV_STATE_DEFAULT);
    // 设置动画时间
    lv_obj_set_style_anim_time(roller, 500, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_set_style_bg_opa(roller, LV_OPA_TRANSP, LV_PART_SELECTED | LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(roller, lv_color_hex(0xFFFFFF), LV_PART_SELECTED | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(roller, FONT_MULTI_ROLLER_SELECTED, LV_PART_SELECTED | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(roller, LV_OPA_COVER, LV_PART_SELECTED | LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(roller, LV_TEXT_ALIGN_CENTER, LV_PART_SELECTED | LV_STATE_DEFAULT);
    lv_obj_add_event_cb(roller, roller_event_handler, LV_EVENT_ALL, NULL);
    lv_obj_add_event_cb(roller, free_struct_cb, LV_EVENT_DELETE, NULL);
    RollerStructType *roller_struct = (RollerStructType *) malloc(sizeof(RollerStructType));
    if (roller_struct == NULL) {
        // 如果分配失败，处理错误
        LV_LOG("roller_struct Memory allocation failed\n");
        return;
    }

    lv_coord_t font_height = lv_font_get_line_height(FONT_TIMES_HZ);
    // 初始化选择的index
    uint8_t selected_index;
    if (roller_type == roller_multi_times) {
        Param.multi.times_roller = roller;
        roller_struct->roller_type = roller_multi_times;
        char *times_str = "t";
        lv_coord_t times_text_width = lv_txt_get_width(times_str, strlen(times_str), FONT_TIMES_HZ, 0,
                                                       LV_TEXT_FLAG_EXPAND);
        lv_obj_t *times_label = lv_label_custom_create(roller_container, times_str, FONT_TIMES_HZ,
                                                       lv_color_hex(Main_Color), LV_TEXT_ALIGN_CENTER);
        lv_obj_set_size(times_label, times_text_width, font_height);
        // 找到 Param.multi.flash_times 的 index
        selected_index = binary_search_index(multi_times_arr, times_arr_size, Param.multi.flash_times);
        // 根据最大值修改 option 列表
        uint8_t max_times = get_max_multi_flash_times(Param.multi.flash_level, Param.multi.flash_freq);
        uint8_t new_arr_size = binary_search_index(multi_times_arr, times_arr_size, max_times) + 1;
        uint8_t new_arr[new_arr_size];
        memcpy(new_arr, multi_times_arr, (new_arr_size) * sizeof(uint8_t));
        char *times_option_new_str = convert_times_arr_to_str(new_arr, (new_arr_size) * sizeof(uint8_t));
        lv_roller_set_options(Param.multi.times_roller, times_option_new_str, LV_ROLLER_MODE_NORMAL);
        free_and_clear(times_option_new_str);
    } else {
        Param.multi.hz_roller = roller;
        roller_struct->roller_type = roller_multi_hz;
        char *hz_str = "Hz";
        lv_coord_t hz_text_width = lv_txt_get_width(hz_str, strlen(hz_str), FONT_TIMES_HZ, 0,
                                                    LV_TEXT_FLAG_EXPAND);
        lv_obj_t *hz_label = lv_label_custom_create(roller_container, hz_str, FONT_TIMES_HZ, lv_color_hex(Main_Color),
                                                    LV_TEXT_ALIGN_CENTER);
        lv_obj_set_size(hz_label, hz_text_width, font_height);
        // 频率roller初始化
        selected_index = binary_search_index(multi_hz_arr, hz_arr_size, Param.multi.flash_freq);
    }
    lv_roller_set_selected(roller, selected_index, LV_ANIM_OFF);
    lv_obj_set_user_data(roller, roller_struct);
    lv_roller_set_visible_row_count(roller, 3);
    border_focused_obj_init(roller_container, &Pages.page[Page_Multi], Focused_Editing_Mode);
    // if (lv_group_get_default() != Pages.page[Page_Multi].indev_group) {
    // 从默认输入组移除roller
    // 一个对象只属于一个输入组，故不需要传入group参数
    lv_group_remove_obj(roller);
    // }
}

void page_multi_init(lv_obj_t *page) {
    // TODO 改水平布局，调行列间距
    lv_obj_t *v_layout = lv_layout_custom_create(page, LV_FLEX_FLOW_COLUMN);
    lv_obj_set_size(v_layout, LV_PCT(100), LV_PCT(100));
    lv_obj_set_style_pad_top(v_layout, status_bar_h + 10, 0);
    lv_obj_set_style_pad_hor(v_layout, 18, 0);
    lv_obj_set_style_pad_gap(v_layout, 0, 0);
    lv_obj_set_style_bg_opa(v_layout, LV_OPA_TRANSP, 0);

    comp_fine_tuning_slider_init(v_layout, ft_slider_multi);

    lv_obj_t *v_layout_sub = lv_layout_custom_create(v_layout, LV_FLEX_FLOW_COLUMN);
    lv_obj_set_style_bg_opa(v_layout_sub, LV_OPA_TRANSP, 0);
    // lv_obj_set_style_pad_hor(v_layout_sub, 15, 0);
    lv_obj_set_style_pad_gap(v_layout_sub, 0, 0);
    lv_obj_set_size(v_layout_sub, LV_PCT(100), Screen_Height - status_bar_h - 10 - 90);
    // lv_obj_custom_set_bg_color(v_layout_sub,Main_Color);

    lv_obj_custom_trans_create(v_layout_sub, 10, 10);
    lv_obj_t *roller_h_layout = lv_layout_custom_create(v_layout_sub, LV_FLEX_FLOW_ROW);
    // lv_obj_set_style_pad_ver(roller_h_layout, 6, 0);
    lv_obj_set_style_pad_gap(roller_h_layout, 12, 0);
    lv_obj_set_style_bg_opa(roller_h_layout, LV_OPA_TRANSP, 0);
    lv_obj_set_size(roller_h_layout, LV_PCT(100), roller_height);
    lv_obj_set_flex_align(roller_h_layout, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_custom_trans_create(v_layout_sub, 10, 4);

    // char *times_option_str = "1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n"
    //                          "11\n12\n13\n14\n15\n16\n17\n18\n19\n20\n"
    //                          "25\n30\n35\n40\n45\n50\n60\n70\n80\n90\n100";
    char *times_option_str = convert_times_arr_to_str(multi_times_arr, times_arr_size);
    char *hz_option_str = convert_times_arr_to_str(multi_hz_arr, hz_arr_size);

    // char *hz_option_str = "1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n"
    //                       "11\n12\n13\n14\n15\n16\n17\n18\n19\n20\n"
    //                       "25\n30\n35\n40\n45\n50\n60\n70\n80\n90\n100\n"
    //                       "110\n120\n130\n140\n150\n160\n170\n180\n190\n200";

    comp_roller_init(roller_h_layout, times_option_str, roller_multi_times);
    free_and_clear(times_option_str);

    comp_roller_init(roller_h_layout, hz_option_str, roller_multi_hz);
    free_and_clear(hz_option_str);
    // lv_obj_t * hz_label = lv_label_custom_create(roller_h_layout, hz_str, FONT_TIMES_HZ, Main_Color,
    //                                              LV_TEXT_ALIGN_CENTER);
    // lv_obj_set_size(hz_label, hz_text_width, font_height);

    comp_page_indicator(v_layout_sub, LV_FLEX_FLOW_ROW);

    comp_group_list_multi(v_layout_sub);

    handle_multi_top = pm_create_handle(page, pos_top);
    handle_multi_right = pm_create_handle(page, pos_right);
}
