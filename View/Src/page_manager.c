//
// Created by <PERSON><PERSON> on 2024/6/15.
//

#include <stdio.h>
#include "page_manager.h"
#include "user.h"

#if IS_REAL_DEVICE

#include "examples/porting/lv_port_indev.h"

#else

#include "../../lv_drivers/win32drv/win32drv.h"

#endif

#include "view_model.h"

PageManagerStructType PageManager;

void pm_click_to_page_control_center() {
    pm_creat_page(PageManager.main_page->pos[pos_top].page, PageManager.main_page, LV_ALIGN_OUT_TOP_MID,
                  lv_color_black());
    page_click_anim(pos_top, &Pages.page[Page_Control_Center], anim_overlay);
}

void pm_creat_page(PageTypeHandle *page_create, PageTypeHandle *align_to_page, lv_align_t align, lv_color_t bg_color) {
    if (page_create != NULL) {
        if (page_create->state == page_valid) {
            page_create->state = page_created;
            page_create->obj = lv_obj_create(PageManager.obj);
            lv_obj_add_style(page_create->obj, &PageManager.default_style, 0);
            lv_obj_set_size(page_create->obj, LV_PCT(100), LV_PCT(100));
            lv_obj_clear_flag(page_create->obj, LV_OBJ_FLAG_SCROLLABLE);
            lv_obj_set_scrollbar_mode(page_create->obj, LV_SCROLLBAR_MODE_OFF);
            // lv_obj_add_flag(page_create->obj, LV_OBJ_FLAG_EVENT_BUBBLE);
            lv_obj_align_to(page_create->obj, align_to_page->obj, align, 0, 0);
            lv_obj_set_style_bg_color(page_create->obj, bg_color, 0);
            lv_obj_set_user_data(page_create->obj, page_create);
            page_create->init_page(page_create->obj);
        } else if (page_create->state == page_created) {
            lv_obj_align_to(page_create->obj, align_to_page->obj, align, 0, 0);
        }
        lv_obj_move_foreground(page_create->obj);
        // 刷新视图，否则上一条函数不生效
        lv_obj_update_layout(page_create->obj);
        defocused_handle(page_create->indev_group);
    }
}

// 页面管理器开始页
void pm_page_start(PageTypeHandle *page) {
    // PageTypeHandle *page_node;

    // 先赋值，避免init_page内的组件调用PageManager.main_page时PageManager.main_page为NULL
    pm_set_main_page(page);
    if (page->state == page_valid) {
        page->obj = lv_obj_create(PageManager.obj);
        lv_obj_add_style(page->obj, &PageManager.default_style, 0);
        lv_obj_set_size(page->obj, LV_PCT(100), LV_PCT(100));
        lv_obj_clear_flag(page->obj, LV_OBJ_FLAG_SCROLLABLE);
        lv_obj_set_scrollbar_mode(page->obj, LV_SCROLLBAR_MODE_OFF);
        lv_obj_align(page->obj, LV_ALIGN_TOP_MID, 0, 0);
        // 蓝色
        // lv_obj_set_style_bg_color(page->obj, lv_color_hex(0x0d77d7), 0);
        lv_obj_set_style_bg_color(page->obj, lv_color_hex(0x000000), 0);
        lv_obj_set_user_data(page->obj, page);
        page->init_page(page->obj);
        defocused_handle(page->indev_group);
        page->state = page_created;
    }

    // if (page->top.page != NULL) {
    //     page_node = page->top.page;
    //     if (page_node->state == page_valid) {
    //         // 紫色
    //         // pm_creat_page(page_node, page, LV_ALIGN_OUT_TOP_MID, lv_color_hex(0x9092f8));
    //         pm_creat_page(page_node, page, LV_ALIGN_OUT_TOP_MID, lv_color_hex(0x000000));
    //     } else if (page_node->state == page_created) {
    //         lv_obj_align_to(page_node->obj, page->obj, LV_ALIGN_OUT_TOP_MID, 0, 0);
    //     }
    // }

    // if (page->bottom.page != NULL) {
    //     page_node = page->bottom.page;
    //     if (page_node->state == page_valid) {
    //         // 蓝绿色
    //         // pm_creat_page(page_node, page, LV_ALIGN_OUT_BOTTOM_MID, lv_color_hex(0x40E0D0));
    //         pm_creat_page(page_node, page, LV_ALIGN_OUT_BOTTOM_MID, lv_color_hex(0x000000));
    //     } else if (page_node->state == page_created) {
    //         lv_obj_align_to(page_node->obj, page->obj, LV_ALIGN_OUT_BOTTOM_MID, 0, 0);
    //     }
    // }

    // if (page->right.page != NULL) {
    //     page_node = page->right.page;
    //     if (page_node->state == page_valid) {
    //         // 绿色
    //         // pm_creat_page(page_node, page, LV_ALIGN_OUT_RIGHT_MID, lv_color_hex(0x20c966));
    //         pm_creat_page(page_node, page, LV_ALIGN_OUT_RIGHT_MID, lv_color_hex(0x000000));
    //     } else if (page_node->state == page_created) {
    //         lv_obj_align_to(page_node->obj, page->obj, LV_ALIGN_OUT_RIGHT_MID, 0, 0);
    //     }
    // }
    //
    // if (page->left.page != NULL) {
    //     page_node = page->left.page;
    //     if (page_node->state == page_valid) {
    //         // 黄色
    //         // pm_creat_page(page_node, page, LV_ALIGN_OUT_LEFT_MID, lv_color_hex(0xdbb236));
    //         pm_creat_page(page_node, page, LV_ALIGN_OUT_LEFT_MID, lv_color_black());
    //     } else if (page_node->state == page_created) {
    //         lv_obj_align_to(page_node->obj, page->obj, LV_ALIGN_OUT_LEFT_MID, 0, 0);
    //     }
    // }

    // if (page->bottom.page != NULL) {
    //     page_node = page->bottom.page;
    //     if (page_node->state == page_valid) {
    //         // 红色
    //         pm_creat_page(page_node, page, LV_ALIGN_OUT_BOTTOM_MID, lv_color_hex(0xed556a));
    //     } else if (page_node->state == page_created) {
    //         lv_obj_align_to(page_node->obj, page->obj, LV_ALIGN_OUT_BOTTOM_MID, 0, 0);
    //     }
    // }
}

// 页面管理器初始化
void pm_init() {
    // PageManager.state = LV_DIR_NONE;
    // PageManager.Dir = LV_DIR_NONE;
    // PageManager.MoveDir = PageNone;
    // PageManager.pos_x = 0;
    // PageManager.pos_y = 0;
    // PageManager.LimitSize = PageLimit;
    // PageManager.LimitSize = Screen_Width * Screen_Height / 100 * PageManager.LimitSize;

    // 初始化样式
    lv_style_init(&PageManager.default_style);
    // 设置内边距为 0
    lv_style_set_pad_all(&PageManager.default_style, 0);
    // 设置背景不透明度为 0
    // lv_style_set_bg_opa(&PageManager.default_style, LV_OPA_0);
    // 设置为直角边
    lv_style_set_radius(&PageManager.default_style, 0);
    // 设置为无边框
    lv_style_set_border_width(&PageManager.default_style, 0);
    // 设置对齐方式为居中
    lv_style_set_align(&PageManager.default_style, LV_ALIGN_CENTER);

    PageManager.obj = lv_obj_create(lv_scr_act());
    // lv_style_set_bg_color(&PageManager.default_style, lv_color_hex(0x000000));
    lv_obj_add_style(PageManager.obj, &PageManager.default_style, 0);
    lv_obj_set_size(PageManager.obj, Screen_Width, Screen_Height);
    lv_obj_clear_flag(PageManager.obj, LV_OBJ_FLAG_SCROLLABLE);
    lv_obj_set_scrollbar_mode(PageManager.obj, LV_SCROLLBAR_MODE_OFF);
}

// 添加页面
void pm_add_page(PageTypeHandle *page_struct, void (*init_page)(lv_obj_t *page)) {
    page_struct->state = page_valid;
    page_struct->init_page = init_page;
    for (int i = 0; i < PosItemCount; ++i) {
        page_struct->pos[i].page = NULL;
        page_struct->pos[i].anim_mode = anim_none;
    }
    // page_struct->DeinitPage = DeinitPage;
    // page_struct->next.page = NULL;
    // page_struct->next.anim_mode = anim_none;
}


// 设置默认输入组
void set_indev_group(lv_group_t *indev_group) {
    // lv_obj_t *focused_obj = lv_group_get_focused(indev_group);
    // if (focused_obj != NULL) {
    //     lv_obj_clear_state(focused_obj, LV_STATE_FOCUSED);
    // }
    // if (indev_group == Pages.page[Page_Control_Center].indev_group) {
    //     lv_group_del(Pages.page[Page_Home].indev_group);
    //     Pages.page[Page_Home].indev_group = NULL;
    //     LV_LOG("---------------true\n");
    // } else {
    //     LV_LOG("---------------false\n");
    // }
#if IS_REAL_DEVICE
    lv_indev_set_group(indev_keypad, indev_group);
    lv_indev_set_group(indev_encoder, indev_group);
#else
    lv_indev_set_group(lv_win32_encoder_device_object, indev_group);
    lv_indev_set_group(lv_win32_keypad_device_object, indev_group);
#endif
    lv_group_set_default(indev_group);
    // 避免页面切换后聚焦在最后一个obj
    lv_group_set_refocus_policy(indev_group, LV_GROUP_REFOCUS_POLICY_NEXT);
    // 禁止焦点循环
    lv_group_set_wrap(indev_group, false);


    // lv_group_focus_obj(NULL);
    // lv_group_focus_freeze(Pages.Page_Home.indev_group, true);
    // TODO 考虑在检测到滚轮滚动时解除焦点冻结
    // lv_group_focus_freeze(lv_group_get_default(), false);
    // lv_group_focus_prev(lv_group_get_default());
    // lv_obj_t *focused_obj = lv_group_get_focused(indev_group);
    // if (focused_obj != NULL) {
    //     LV_LOG("---------------%d\n", lv_obj_get_state(focused_obj));
    //     lv_obj_clear_state(focused_obj, LV_STATE_FOCUSED);  // 清除焦点
    //     lv_obj_clear_state(focused_obj, LV_STATE_FOCUS_KEY);  // 清除焦点
    // }
    // lv_indev_set_group(lv_win32_encoder_device_object, indev_group);
    // if (lv_group_get_default() == Pages.page[Page_Control_Center].indev_group) {
    //     lv_group_del(Pages.page[Page_Home].indev_group);
    //     Pages.page[Page_Home].indev_group = NULL;
    //     LV_LOG("---------------true\n");
    // } else {
    //     LV_LOG("---------------false\n");
    // }
    // if (PageManager.main_page == &Pages.Page_Home) {
    //     LV_LOG("---------------Pages.Page_Home\n");
    // } else {
    //     LV_LOG("---------------unknow\n");
    // }
    // LV_LOG("---------------%d\n", Pages.Page_Home.test);
    // LV_LOG("---------------%d\n", PageManager.main_page->test);

}

void pm_set_main_page(PageTypeHandle *main_page) {
    // if (lv_obj_is_valid(Pages.ch_layout)) {
    //     lv_obj_del(Pages.ch_layout);
    //     Pages.ch_layout = NULL;
    //     set_RF_status();
    // }
    if (main_page == &Pages.page[Page_Multi]) {
        if (!Param.is_Multi_page) {
            set_multi_param();
        }
        Param.is_Multi_page = true;
    } else if (main_page == &Pages.page[Page_Home]) {
        // if (Param.is_Multi_page || PageManager.main_page == &Pages.page[Page_Welcome]) {
        if (Param.is_Multi_page) {
            set_flash_mode_level();
        }
        Param.is_Multi_page = false;
    }
    PageManager.main_page = main_page;
    // main_page->indev_group = lv_group_create();
    if (main_page->indev_group == NULL) {
        main_page->indev_group = lv_group_create();
    }
    set_indev_group(main_page->indev_group);

    // // TODO 切换页面才设置输入组,此时对象已创建,会不生效,须二次进入页面才生效
    // lv_indev_set_group(lv_win32_encoder_device_object, main_page->indev_group);
    // lv_indev_set_group(lv_win32_keypad_device_object, main_page->indev_group);
    // if (PageManager.main_page == &Pages.Page_Home) {
    //     LV_LOG("---------------Pages.Page_Home\n");
    // } else {
    //     LV_LOG("---------------unknow\n");
    // }
    // if (main_page->indev_group == Pages.Page_Home.indev_group) {
    //     LV_LOG("---------------true\n");
    // } else {
    //     LV_LOG("---------------false\n");
    // }
    // main_page->indev_group = lv_group_create();
    // lv_group_set_default(main_page->indev_group);
    // lv_indev_set_group(lv_win32_encoder_device_object, main_page->indev_group);
}

