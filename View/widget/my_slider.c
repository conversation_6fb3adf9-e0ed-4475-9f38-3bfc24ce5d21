/**
 * @file my_slider.c
 *
 */

/*********************
 *      INCLUDES
 *********************/
#include "my_slider_private.h"
#include "src/misc/lv_area.h"
#include "src/core/lv_obj.h"
#include "src/core/lv_event.h"
#include "src/core/lv_obj_class.h"

#if LV_USE_SLIDER != 0

#include "src/misc/lv_assert.h"
#include "src/core/lv_group.h"
#include "src/core/lv_indev.h"
#include "src/draw/lv_draw.h"
#include "src/misc/lv_math.h"
#include "src/core/lv_disp.h"
#include "src/widgets/lv_img.h"

/*********************
 *      DEFINES
 *********************/
#define MY_CLASS (&my_slider_class)

#define LV_SLIDER_KNOB_COORD(is_reversed, area) (is_reversed ? area.x1 : area.x2)
#define LV_SLIDER_KNOB_COORD_VERTICAL(is_reversed, area) (is_reversed ? area.y2 : area.y1)

/**********************
 *      TYPEDEFS
 **********************/

/**********************
 *  STATIC PROTOTYPES
 **********************/
static void my_slider_constructor(const lv_obj_class_t *class_p, lv_obj_t *obj);
static void my_slider_event(const lv_obj_class_t *class_p, lv_event_t *e);
static bool is_slider_horizontal(lv_obj_t *obj);
// static void draw_knob(lv_event_t *e);
static void drag_start(lv_obj_t *obj);
static void update_knob_pos(lv_obj_t *obj, bool check_drag);

/**********************
 *  STATIC VARIABLES
 **********************/
const lv_obj_class_t my_slider_class = {
	.constructor_cb = my_slider_constructor,
	.event_cb = my_slider_event,
	.editable = LV_OBJ_CLASS_EDITABLE_TRUE,
	.group_def = LV_OBJ_CLASS_GROUP_DEF_TRUE,
    // .theme_inheritable = LV_OBJ_CLASS_THEME_INHERITABLE_TRUE,
	.instance_size = sizeof(my_slider_t),
	.base_class = &lv_slider_class,
	// .name = "my_slider",
};

/**********************
 *      MACROS
 **********************/

/**********************
 *   GLOBAL FUNCTIONS
 **********************/

lv_obj_t *my_slider_create(lv_obj_t *parent)
{
	LV_LOG_INFO("begin\n");
	lv_obj_t *obj = lv_obj_class_create_obj(MY_CLASS, parent);
	lv_obj_class_init_obj(obj);
	return obj;
}

bool my_slider_is_dragged(const lv_obj_t *obj)
{
	return lv_slider_is_dragged(obj);
}

void my_slider_set_value(lv_obj_t *obj, int32_t value, lv_anim_enable_t anim)
{
	lv_slider_set_value(obj, value, anim);
}

void my_slider_set_left_value(lv_obj_t *obj, int32_t value, lv_anim_enable_t anim)
{
	lv_slider_set_left_value(obj, value, anim);
}

void my_slider_set_range(lv_obj_t *obj, int32_t min, int32_t max)
{
	lv_slider_set_range(obj, min, max);
}

int32_t my_slider_get_value(const lv_obj_t *obj)
{
	return lv_slider_get_value(obj);
}

int32_t my_slider_get_left_value(const lv_obj_t *obj)
{
	return lv_slider_get_left_value(obj);
}

int32_t my_slider_get_min_value(const lv_obj_t *obj)
{
	return lv_slider_get_min_value(obj);
}

int32_t my_slider_get_max_value(const lv_obj_t *obj)
{
	return lv_slider_get_max_value(obj);
}

// lv_slider_mode_t my_slider_get_mode(lv_obj_t * slider)
//{
//      lv_bar_mode_t mode = lv_bar_get_mode(slider);
//      if(mode == LV_BAR_MODE_SYMMETRICAL) return LV_SLIDER_MODE_SYMMETRICAL;
//      else if(mode == LV_BAR_MODE_RANGE) return LV_SLIDER_MODE_RANGE;
//      else return LV_SLIDER_MODE_NORMAL;
//  }

/**********************
 *   STATIC FUNCTIONS
 **********************/

static void my_slider_constructor(const lv_obj_class_t *class_p, lv_obj_t *obj)
{
	LV_UNUSED(class_p);

	my_slider_t *my_slider = (my_slider_t *)obj;
	/* 1. 先调用父类（lv_slider）的构造函数，确保原生Slider的基础初始化完成 */
	my_slider_class.base_class->constructor_cb(class_p, &my_slider->bar);

	/* 2. 再执行子类（my_slider）的自定义初始化逻辑 */
	my_slider->drag_start_value = 0;
	my_slider->bar.bar.mode = LV_SLIDER_MODE_NORMAL; // 设置普通模式
}

static void my_slider_event(const lv_obj_class_t *class_p, lv_event_t *e)
{
	LV_UNUSED(class_p);

	lv_res_t res;

	lv_event_code_t code = lv_event_get_code(e);
	lv_obj_t *obj = lv_event_get_current_target(e); // 获取当前触发的对象
	
	my_slider_t *my_slider = (my_slider_t *)obj;

	if (code == LV_EVENT_PRESSING)
	{
		update_knob_pos(obj, true);
	}
	else if (code == LV_EVENT_RELEASED || code == LV_EVENT_PRESS_LOST)
	{
		update_knob_pos(obj, false);
		my_slider->bar.dragging = false;
		my_slider->bar.value_to_set = NULL;

		lv_obj_invalidate(obj);

		/*Leave edit mode if released. (No need to wait for LONG_PRESS)*/
		lv_group_t *g = lv_obj_get_group(obj);
		bool editing = lv_group_get_editing(g);
		lv_indev_type_t indev_type = lv_indev_get_type(lv_indev_get_act());
		if (indev_type == LV_INDEV_TYPE_ENCODER)
		{
			if (editing)
			{
				lv_group_set_editing(g, false);
			}
		}
		else if (indev_type == LV_INDEV_TYPE_POINTER)
		{
			if (is_slider_horizontal(obj))
				lv_obj_add_flag(obj, LV_OBJ_FLAG_SCROLL_CHAIN_VER);
			else
				lv_obj_add_flag(obj, LV_OBJ_FLAG_SCROLL_CHAIN_HOR);
		}
	}
	else
	{
		// 其他调用父类函数
		/*Call the ancestor's event handler*/
		res = lv_obj_event_base(MY_CLASS, e);
		if (res != LV_RES_OK)
			return;
	}
}


static bool is_slider_horizontal(lv_obj_t *obj)
{
	return lv_obj_get_width(obj) >= lv_obj_get_height(obj);
}

/**
 * @brief 只有LV_SLIDER_MODE_NORMAL模式使用
 *
 * @param obj
 */
static void drag_start(lv_obj_t *obj)
{
	my_slider_t *my_slider = (my_slider_t *)obj;

	my_slider->bar.dragging = true;
	/* 直接拖动当前值 */
	my_slider->bar.value_to_set = &my_slider->bar.bar.cur_value;

	/* 保存当前 knob 的起始值和起始坐标，供 update_knob_pos 用 */
	my_slider->drag_start_value = *my_slider->bar.value_to_set;
	lv_indev_get_point(lv_indev_get_act(), &my_slider->drag_start_point);
	// lv_obj_transform_point(obj, &my_slider->drag_start_point, LV_OBJ_POINT_TRANSFORM_FLAG_INVERSE_RECURSIVE);
}

/**
 * @brief 更新滑块位置（修改版：点击轨道不会跳，拖拽相对变化）
 *
 * @param obj
 * @param check_drag 是否检测拖拽
 */
static void update_knob_pos(lv_obj_t *obj, bool check_drag)
{
	my_slider_t *my_slider = (my_slider_t *)obj;
	lv_indev_t *indev = lv_indev_get_act();
	if (lv_indev_get_type(indev) != LV_INDEV_TYPE_POINTER)
		return;
	if (lv_indev_get_scroll_obj(indev) != NULL)
		return;

	lv_point_t p;
	lv_indev_get_point(indev, &p);
	// lv_obj_transform_point(obj, &p, LV_OBJ_POINT_TRANSFORM_FLAG_INVERSE_RECURSIVE);

	bool is_hor = is_slider_horizontal(obj);

	if (check_drag && !my_slider->bar.dragging)
	{
		// int32_t ofs = is_hor ? (p.x - my_slider->bar.pressed_point.x) : (p.y - my_slider->bar.pressed_point.y);
		//
		// /*Stop processing when offset is below scroll_limit*/
		// if (LV_ABS(ofs) < indev->scroll_limit)
		// {
		// 	return;
		// }

		/* 开始拖拽：记录起始位置和起始值 */
		drag_start(obj);
		my_slider->drag_start_value = *(&my_slider->bar.bar.cur_value); /* 只操作当前 knob */
		my_slider->drag_start_point = p;
	}

	if (!my_slider->bar.dragging || !my_slider->bar.value_to_set)
		return;

	int32_t new_value = my_slider->drag_start_value;
	const int32_t range = my_slider->bar.bar.max_value - my_slider->bar.bar.min_value;

	if (is_hor)
	{
		/* 水平方向：根据拖动距离换算 */
		int32_t bg_left = lv_obj_get_style_pad_left(obj, LV_PART_MAIN);
		int32_t bg_right = lv_obj_get_style_pad_right(obj, LV_PART_MAIN);
		int32_t w = lv_obj_get_width(obj) - bg_left - bg_right;

		int32_t diff = p.x - my_slider->drag_start_point.x;
		int32_t delta_val = (diff * range) / (w ? w : 1);
		new_value = my_slider->drag_start_value + delta_val;
	}
	else
	{
		/* 垂直方向：Y方向向上为正 */
		int32_t bg_top = lv_obj_get_style_pad_top(obj, LV_PART_MAIN);
		int32_t bg_bottom = lv_obj_get_style_pad_bottom(obj, LV_PART_MAIN);
		int32_t h = lv_obj_get_height(obj) - bg_top - bg_bottom;

		int32_t diff = my_slider->drag_start_point.y - p.y;
		int32_t delta_val = (diff * range) / (h ? h : 1);
		new_value = my_slider->drag_start_value + delta_val;
	}

	int32_t real_max_value = my_slider->bar.bar.max_value;
	int32_t real_min_value = my_slider->bar.bar.min_value;

	new_value = LV_CLAMP(real_min_value, new_value, real_max_value);
	if (*my_slider->bar.value_to_set != new_value)
	{
		*my_slider->bar.value_to_set = new_value;
		if (is_hor)
			lv_obj_clear_flag(obj, LV_OBJ_FLAG_SCROLL_CHAIN_VER);
		else
			lv_obj_clear_flag(obj, LV_OBJ_FLAG_SCROLL_CHAIN_HOR);

		lv_obj_invalidate(obj);
		lv_res_t res = lv_event_send(obj, LV_EVENT_VALUE_CHANGED, NULL);
		if (res != LV_RES_OK)
			return;
	}
}

#endif
