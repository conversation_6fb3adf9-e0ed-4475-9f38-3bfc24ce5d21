/**
 * @file my_slider_private.h
 *
 */

#ifndef MY_SLIDER_PRIVATE_H
#define MY_SLIDER_PRIVATE_H

#ifdef __cplusplus
extern "C" {
#endif

/*********************
 *      INCLUDES
 *********************/

#include "src/widgets/lv_slider.h"
#include "my_slider.h"

//#if LV_USE_MY_SLIDER != 0

/*********************
 *      DEFINES
 *********************/

/**********************
 *      TYPEDEFS
 **********************/

typedef struct{
    lv_slider_t bar;                 /**< Add the ancestor's type first */
    int32_t drag_start_value;   // 拖拽时 knob 的起始值
    lv_point_t drag_start_point; // 拖拽时起始点
}my_slider_t;


/**********************
 * GLOBAL PROTOTYPES
 **********************/

/**********************
 *      MACROS
 **********************/

//#endif /* LV_USE_MY_SLIDER != 0 */

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif /*MY_SLIDER_PRIVATE_H*/
