//
// Created by <PERSON><PERSON> on 2024/6/15.
//

#ifndef LV_PORT_WIN_CODEBLOCKS_V8_PAGE_MANAGER_H
#define LV_PORT_WIN_CODEBLOCKS_V8_PAGE_MANAGER_H

// #include "user.h"
#include "lvgl.h"

#define Screen_Width    368
#define Screen_Height   448

// 页面切换的触发阈值百分比(1-100)，越小越容易触发
#define PageLimit   25
// 页面切换速度
#define AnimMoveSpeed   800
// 页面切换动画模式
#define AnimMoveMode    lv_anim_path_ease_in_out

// 页面状态
enum PageState {
    // 无效页面
    page_invalid,
    // 有效页面
    page_valid,
    // 有效并已创建的页面
    page_created,
};

// 页面的位置方向
// enum PageDir {
//     // 本页面
//     PageNone = 0,
//     // 上面的页面
//     PageUp = (1 << 0),
//     // 下面的页面
//     PageDown = (1 << 1),
//     // 左面的页面
//     PageLeft = (1 << 2),
//     // 右面的页面
//     PageRight = (1 << 3),
// };

typedef enum {
    pos_top,
    pos_bottom,
    pos_left,
    pos_right,
    pos_next,
    PosItemCount
} PositionEnum;

// 页面动画模式
typedef enum {
    // 没有切换方式
    anim_none,
    // 平推
    anim_slide,
    // 覆盖
    anim_overlay,
    // 淡入
    anim_fade_in,
} AnimModeEnum;

typedef enum {
    opr_value_sub,
    opr_value_add
} OprValueEnum;

// 存储聚焦对象最大值
#define GROUP_OBJ_MAX   30

typedef struct PageNode {
    enum PageState state;
    lv_obj_t *obj;

    void (*init_page)(lv_obj_t *page_obj);

    struct {
        struct PageNode *page;
        AnimModeEnum anim_mode;
    } pos[PosItemCount];

    // 记录覆盖状态
    bool is_overlay_active;
    // 记录回推手柄位置
    uint8_t overlay_pos;
    // 记录被覆盖页
    struct PageNode *overlay_page;
    // 设置该页面始终会覆盖其他页的标志
    bool is_always_overlay;

    // 每个页面的输入组
    lv_group_t *indev_group;
    lv_obj_t *group_obj[GROUP_OBJ_MAX];
    // lv_group_t *group_obj1;
} PageTypeHandle;

// pm结构体
typedef struct {
    // 以下的方向均指目标页面相对与主页的位置，而不是主页运动方向，与主页实际运动方向相反
    // 实际运动方向
    // enum PageDir MoveDir;
    // 实际允许运动方向
    // uint8_t State;
    // 允许运动方向
    // uint8_t Dir;
    // 管理器主体
    lv_obj_t *obj;
    // 管理器背景层
    // lv_obj_t *BackGround;
    // 默认管理器样式
    lv_style_t default_style;
    // 永远指向无触摸时显示的页面
    PageTypeHandle *main_page;
    // // 动画结构体
    // lv_anim_t Anim;
    // int32_t xStart;
    // int32_t yStart;
    // int32_t pos_x;
    // int32_t pos_y;
    // int32_t LimitSize;
} PageManagerStructType;
extern PageManagerStructType PageManager;

void pm_init();

void pm_page_start(PageTypeHandle *page);

void pm_add_page(PageTypeHandle *page_struct, void (*init_page)(lv_obj_t *page));

void pm_creat_page(PageTypeHandle *page_create, PageTypeHandle *align_to_page, lv_align_t align, lv_color_t bg_color);

void set_indev_group(lv_group_t *indev_group);

void pm_set_main_page(PageTypeHandle *main_page);

void pm_click_to_page_control_center();

#endif //LV_PORT_WIN_CODEBLOCKS_V8_PAGE_MANAGER_H
