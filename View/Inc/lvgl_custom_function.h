//
// Created by <PERSON><PERSON> on 2024/7/8.
//

#ifndef LV_PORT_WIN_CODEBLOCKS_V8_LVGL_CUSTOM_FUNCTION_H
#define LV_PORT_WIN_CODEBLOCKS_V8_LVGL_CUSTOM_FUNCTION_H

#include "lvgl.h"
#include "user.h"

#define Short_Click_Sensitivity 10

// #define Bg_Color_Black 0x2E2F31
#define Bg_Color_Black 0x191919
#define Bg_Color_Gray 0x303030
// #define Bg_Color_Gray 0x3D3D3D
#define Main_Color 0x109BFF
#define Main_Color_Opt 0x0B61B5
#define TTL_Color 0xFAFF00
#define M_Color 0xFFFFFF
#define OFF_Color 0xCE1308
#define Charge_Color 0x00EA09
#define BAT_LOW_Color 0xFF0000
// #define OFF_Color 0xFF3B30
#define Gradient_Start_Color 0x1EECF9
#define Indicator_Gray_Color 0x767677
#define Unselected_Label_Color 0x767677
#define Unselected_Bg_Color 0x292929
#define Setting_Info_Color 0xFAFF00

#define COLOR_GROUP_A   0xFF0000
#define COLOR_GROUP_B   0xF7FF00
#define COLOR_GROUP_C   0x00FF55
#define COLOR_GROUP_D   0x00CCFF
#define COLOR_GROUP_E   0xFF00FF

LV_IMG_DECLARE(ico_logo)
// LV_IMG_DECLARE(icon_electricity_level_100)

LV_IMG_DECLARE(icon_flash)
LV_IMG_DECLARE(icon_lamp)
#if ProductModel == QZ_F
LV_IMG_DECLARE(icon_zoom)
#endif
LV_IMG_DECLARE(icon_add_40)
LV_IMG_DECLARE(icon_reduce_40)
LV_IMG_DECLARE(icon_more_groups)
LV_IMG_DECLARE(icon_vol_on)
LV_IMG_DECLARE(icon_vol_off)
LV_IMG_DECLARE(icon_locked)
LV_IMG_DECLARE(icon_unlock)
LV_IMG_DECLARE(icon_setting)
LV_IMG_DECLARE(icon_rf)
LV_IMG_DECLARE(icon_screen_setting)
LV_IMG_DECLARE(icon_bluetooth)
LV_IMG_DECLARE(icon_language)
LV_IMG_DECLARE(icon_reset)
// LV_IMG_DECLARE(icon_disp_type)
LV_IMG_DECLARE(icon_power_disp)
// LV_IMG_DECLARE(icon_fractions)
// LV_IMG_DECLARE(icon_decimals)
LV_IMG_DECLARE(icon_front_curtain)
LV_IMG_DECLARE(icon_rear_curtain)
LV_IMG_DECLARE(icon_high_speed)
LV_IMG_DECLARE(icon_range_close)
LV_IMG_DECLARE(icon_range_long)
LV_IMG_DECLARE(icon_loading)
LV_IMG_DECLARE(icon_locked_100)
LV_IMG_DECLARE(icon_restore_factory)
LV_IMG_DECLARE(icon_restore_factory_settings)
LV_IMG_DECLARE(icon_synchronization)
LV_IMG_DECLARE(icon_brightness)
// LV_IMG_DECLARE(img_translucent_mask_bg)
// LV_IMG_DECLARE(icon_add_36)
// LV_IMG_DECLARE(icon_reduce_36)
LV_IMG_DECLARE(icon_lamp_36)
LV_IMG_DECLARE(icon_front_curtain_36)
LV_IMG_DECLARE(icon_rear_curtain_36)
LV_IMG_DECLARE(icon_high_speed_36)
LV_IMG_DECLARE(icon_electricity_level_0)
LV_IMG_DECLARE(icon_electricity_level_1)
LV_IMG_DECLARE(icon_electricity_level_2)
LV_IMG_DECLARE(icon_electricity_level_3)
LV_IMG_DECLARE(icon_chg_complete)
LV_IMG_DECLARE(icon_battery_low)
LV_IMG_DECLARE(icon_up_20)
LV_IMG_DECLARE(icon_mid_20)
LV_IMG_DECLARE(icon_down_20)
LV_IMG_DECLARE(icon_camera_comm)
LV_IMG_DECLARE(icon_single_contact)
LV_IMG_DECLARE(icon_charging)
LV_IMG_DECLARE(icon_hotshoe)
#if 0
LV_IMG_DECLARE(icon_sleep)
#endif
LV_IMG_DECLARE(icon_grp_color_selected)
LV_IMG_DECLARE(icon_grp_color_unselected)

LV_FONT_DECLARE(font_source_han_sans_bold_32)
LV_FONT_DECLARE(font_source_han_sans_bold_40)
LV_FONT_DECLARE(font_source_han_sans_bold_50)
LV_FONT_DECLARE(font_source_han_sans_regular_22)
LV_FONT_DECLARE(font_source_han_sans_regular_30)
LV_FONT_DECLARE(font_source_han_sans_regular_40)
LV_FONT_DECLARE(font_source_han_sans_regular_50)
LV_FONT_DECLARE(font_source_han_sans_medium_18)

// Regular 22
#define FONT_SLIDER_SUB_LEVEL           &font_source_han_sans_regular_22
#define FONT_STATUS_BAR                 &font_source_han_sans_regular_22
#define FONT_TIMES_HZ                   &font_source_han_sans_regular_22
#define FONT_SETTING_ROLLER_UNSELECTED  &font_source_han_sans_regular_22
#define FONT_POP_WINDOW_INFO            &font_source_han_sans_regular_22
// Regular 30
#define FONT_PRODUCT_MODEL              &font_source_han_sans_regular_30
#define FONT_VER                        &font_source_han_sans_regular_30
#define FONT_MULTI_ROLLER_UNSELECTED    &font_source_han_sans_regular_30
#define FONT_SETTING_GRID               &font_source_han_sans_regular_30
#define FONT_SETTING_ROLLER_TITLE       &font_source_han_sans_regular_30
#define FONT_POP_WINDOW_TITLE           &font_source_han_sans_regular_30
#define FONT_BUTTON                     &font_source_han_sans_regular_30
// Regular 40
#define FONT_MENU_OPTION                &font_source_han_sans_regular_40
// Regular 50
#define FONT_HOME_GRP                   &font_source_han_sans_regular_50
// Bold 32
#define FONT_SETTING                    &font_source_han_sans_bold_32
#define FONT_SETTING_ROLLER_SELECTED    &font_source_han_sans_bold_32
// Bold 40
#define FONT_SLIDER_LEVEL               &font_source_han_sans_bold_40
#define FONT_MULTI_ROLLER_SELECTED      &font_source_han_sans_bold_40
#define FONT_SETTING_ITEM               &font_source_han_sans_bold_40
#define FONT_BRIGHTNESS_LEVEL           &font_source_han_sans_bold_40
#define FONT_GRP_MODE                   &font_source_han_sans_bold_40
// Bold 50
#define FONT_MULTI_LEVEL                &font_source_han_sans_bold_50
//Medium 18
#define FONT_TCM                        &font_source_han_sans_medium_18

lv_obj_t *lv_layout_custom_create(lv_obj_t *parent, lv_flex_flow_t flow);

lv_obj_t *lv_label_custom_create(lv_obj_t *parent, char *str, const lv_font_t *font,
                                 lv_color_t color, lv_text_align_t align);

lv_obj_t *lv_img_custom_create(lv_obj_t *parent, const void *img_src);

void lv_obj_custom_set_bg_color(lv_obj_t *obj, lv_color_t color);

lv_obj_t *lv_obj_custom_trans_create(lv_obj_t *parent, lv_coord_t width, lv_coord_t height);

lv_obj_t *lv_line_custom_creat(lv_obj_t *parent, lv_coord_t width, lv_coord_t height, lv_coord_t radius);

#endif //LV_PORT_WIN_CODEBLOCKS_V8_LVGL_CUSTOM_FUNCTION_H
