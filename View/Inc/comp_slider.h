//
// Created by <PERSON><PERSON> on 2024/7/2.
//

#ifndef LV_PORT_WIN_CODEBLOCKS_V8_COMP_SLIDER_H
#define LV_PORT_WIN_CODEBLOCKS_V8_COMP_SLIDER_H

#include "user.h"

extern const lv_coord_t opt_slider_layout_height;

typedef enum {
    slider_event_none,
    slider_event_slider,
    slider_event_list
} SliderEventEnumType;

void slider_layout_home(lv_obj_t *parent);

lv_obj_t *slider_layout_grp_page(lv_obj_t *page, GroupStruct *grp);

#endif //LV_PORT_WIN_CODEBLOCKS_V8_COMP_SLIDER_H
