//
// Created by <PERSON><PERSON> on 2024/6/14.
//

#include "user.h"
// #include "demos/lv_demos.h"
#include "page_grp_switch.h"
#include "page_setting.h"
#include "page_setting_detail.h"
#include "utils.h"

#if ProductModel == QZ_N
Version Ver = {1, 0, 2};
#else
Version Ver = {0, 0, 1};
#endif

ParamStruct Param;
PagesStruct Pages;
SettingStruct Setting;
SettingObjsStruct SettingObjs;
StatusBarStruct StatusBar;

char *group_text[] = {"A", "B", "C", "D", "E"};
char *mode_text[] = {"OFF", "TTL", "M"};
char *flash_level_M_text[] = {"1/512", "1/256", "1/128", "1/64", "1/32", "1/16", "1/8", "1/4", "1/2", "1/1"};
// 20mm兼容NW700
char *zoom_level_text[] = {"20mm", "24mm", "28mm", "35mm", "50mm", "70mm", "80mm", "105mm", "135mm", "200mm"};
#if ProductModel == QZ_F
char *zoom_aps_level_text[] = {"13mm", "16mm", "18mm", "23mm", "33mm", "46mm", "53mm", "69mm", "88mm", "133mm"};
#endif
const uint8_t TTL_flash_level[] = {3, 7, 10, 13, 17, 20, 23, 27, 30, 33, 37, 40, 43, 47, 50};
uint8_t multi_times_arr[] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10,
                             11, 12, 13, 14, 15, 16, 17, 18, 19, 20,
                             25, 30, 35, 40, 45, 50, 60, 70, 80, 90, 100};

uint8_t multi_hz_arr[] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10,
                          11, 12, 13, 14, 15, 16, 17, 18, 19, 20,
                          25, 30, 35, 40, 45, 50, 60, 70, 80, 90, 100,
                          110, 120, 130, 140, 150, 160, 170, 180, 190, 199};

uint8_t times_arr_size = sizeof(multi_times_arr) / sizeof(multi_times_arr[0]);
uint8_t hz_arr_size = sizeof(multi_hz_arr) / sizeof(multi_hz_arr[0]);
uint8_t max_level[MAX_FLASH_LEVEL_INDEX] = {70, 80, 90};
// const int8_t TTL_flash_level_[] = {-30, -27, -23, -20, -17, -13, -10, -7, -3,
//                                    0,
//                                    3, 7, 10, 13, 17, 20, 23, 27, 30};

void param_init() {
    for (int i = 0; i < GroupItemCount; ++i) {
        Param.group[i].group_name = i;
        // Param.group[i].flash_level_M_tcm = int8_2_uint8(0);
    }
    Anim.time_limit = 150;
#if ProductModel == QZ_N
    Param.product_model = "QZ-N";
    Setting.last_sync_value = Setting.values[setting_sync];
#else
    Param.product_model = "QZ-F";
#endif
    if (!Param.brightness.level) {
        Param.brightness.level = MAX_BRIGHTNESS_LEVEL;
    }
    // Param.battery_status = battery_low;
    // Param.electricity_level = 3;
#if !IS_REAL_DEVICE
    for (int i = 0; i < GroupItemCount; ++i) {
        Param.group[i].mode = mode_M;
        Param.group[i].is_added_to_list = true;
        Param.group[i].is_turn_on_Multi = true;
        Param.group[i].flash_level_TTL = ZERO_TTL;
    }
    Param.multi.flash_times = 1;
    Param.multi.flash_freq = 1;
#endif
    Setting.values[setting_zoom] = Zoom_Item_Count;
    Setting.last_min_power = Setting.roller_values[roller_min_power];
}

// lv_group_t *indev_group;

void user_main() {

    // indev_group = lv_group_create();
    // lv_group_set_default(indev_group);
    // lv_group_del(indev_group);
    // lv_group_set_default(NULL);
    // lv_win32_add_all_input_devices_to_group(group);
    // lv_indev_set_group(lv_win32_encoder_device_object, lv_group_get_default());
    // lv_indev_set_group(lv_win32_keypad_device_object, lv_group_get_default());

    param_init();
    // lv_demo_music();
    // lv_demo_widgets();

    // my_app_init();

    // lv_example_control_center();

    pm_init();
    pm_add_page(&Pages.page[Page_Control_Center], page_control_center_init);
    pm_add_page(&Pages.page[Page_Home], page_home_init);
    pm_add_page(&Pages.page[Page_Welcome], page_welcome_init);
    pm_add_page(&Pages.page[Page_Group_Info], page_group_info_init);
    pm_add_page(&Pages.page[Page_Multi], page_multi_init);
    pm_add_page(&Pages.page[Page_Group_Switch], page_grp_switch_init);
    pm_add_page(&Pages.page[Page_Setting], page_setting_init);
    pm_add_page(&Pages.page[Page_Setting_Detail], page_setting_detail_init);

    Pages.page[Page_Home].pos[pos_top].page = &Pages.page[Page_Control_Center];
    Pages.page[Page_Home].pos[pos_top].anim_mode = anim_overlay;
    Pages.page[Page_Home].pos[pos_bottom].page = &Pages.page[Page_Group_Info];
    Pages.page[Page_Home].pos[pos_bottom].anim_mode = anim_slide;
    Pages.page[Page_Home].pos[pos_next].page = &Pages.page[Page_Group_Switch];
    Pages.page[Page_Home].pos[pos_next].anim_mode = anim_slide;
    Pages.page[Page_Home].pos[pos_left].page = &Pages.page[Page_Multi];
    Pages.page[Page_Home].pos[pos_left].anim_mode = anim_slide;
    // Page_Home.left.page = &Page_Group_Info;
    // Page_Home.left.anim_mode = anim_slide;

    Pages.page[Page_Control_Center].pos[pos_bottom].page = &Pages.page[Page_Home];
    Pages.page[Page_Control_Center].pos[pos_bottom].anim_mode = anim_overlay;
    Pages.page[Page_Control_Center].pos[pos_right].page = &Pages.page[Page_Setting];
    Pages.page[Page_Control_Center].pos[pos_right].anim_mode = anim_slide;

    Pages.page[Page_Group_Info].pos[pos_left].page = &Pages.page[Page_Home];
    Pages.page[Page_Group_Info].pos[pos_left].anim_mode = anim_slide;
    Pages.page[Page_Group_Info].pos[pos_top].page = &Pages.page[Page_Control_Center];
    Pages.page[Page_Group_Info].pos[pos_top].anim_mode = anim_overlay;

    Pages.page[Page_Multi].pos[pos_top].page = &Pages.page[Page_Control_Center];
    Pages.page[Page_Multi].pos[pos_top].anim_mode = anim_overlay;
    Pages.page[Page_Multi].pos[pos_right].page = &Pages.page[Page_Home];
    Pages.page[Page_Multi].pos[pos_right].anim_mode = anim_slide;
    // Page_Welcome.next.page = &Page_Home;
    // Page_Home.next.anim_mode = anim_fade_in;

    Pages.page[Page_Group_Switch].pos[pos_left].page = &Pages.page[Page_Home];
    Pages.page[Page_Group_Switch].pos[pos_left].anim_mode = anim_slide;

    Pages.page[Page_Setting].pos[pos_bottom].page = &Pages.page[Page_Home];
    Pages.page[Page_Setting].pos[pos_bottom].anim_mode = anim_overlay;
    Pages.page[Page_Setting].pos[pos_right].page = &Pages.page[Page_Setting_Detail];
    Pages.page[Page_Setting].pos[pos_right].anim_mode = anim_overlay;
    // 设置该页面始终会覆盖其他页的标志
    Pages.page[Page_Setting].is_always_overlay = true;

    Pages.page[Page_Setting_Detail].pos[pos_left].page = &Pages.page[Page_Setting];
    Pages.page[Page_Setting_Detail].pos[pos_left].anim_mode = anim_overlay;

    Anim.is_finished_anim = true;

    // Pages.Page_Home.indev_group = lv_group_create();
    // Pages.Page_Multi.indev_group = lv_group_create();

    pm_page_start(&Pages.page[Page_Home]);
    // pm_page_start(&Pages.page[Page_Welcome]);

    // pm_page_start(&Page_Control_Center);
    // pm_add_page(&Page_Home, page_control_center_init);

    // pm_add_page(&Page_Control_Center, page_control_center_init);
    // icon_replace_demo_2();
}
